import yaml

class Config:
    def __init__(self,file_path):
        # Initialize the YAMLConfig with the path to the YAML file
        self.file_path = file_path 
        # Load the initial configuration from the YAML file
        self.config = self._read_config()

    def _read_config(self):
        try:
            # Attempt to read the YAML file
            with open(self.file_path, 'r') as file:
                # Load the YAML content into a dictionary
                config = yaml.safe_load(file)
                # Return the configuration, or an empty dictionary if the file is empty
                return config if config else {}
        except FileNotFoundError:
            # Handle the case where the file is not found
            print(f"File not found at {self.file_path}")
            return {}

    def get_value(self, key_path):
        """
        Retrieve a nested value from the configuration using a key path.

        Parameters:
        - key_path (list): List of keys representing the path to the desired value.

        Returns:
        - current_data: The value found at the specified key path.
        """
        current_data = self.config
        try:
            # Iterate through the keys in the key path
            for key in key_path:
                # Update current_data to the value associated with the current key
                current_data = current_data.get(key, {})
        except:
            # Attempt to retrieve the value using direct key access
            current_data = current_data.get(key_path[0], {}).get(key_path[1], [])
        return current_data

    def update_value(self, key: str, value):
        """Update a value in the configuration."""
        # Update the configuration with the new key-value pair
        self.config[key] = value
        # Write the updated configuration to the YAML file
        self._write_config()

    def _write_config(self):
        """Write the updated configuration to the YAML file."""
        try:
            # Attempt to write the updated configuration to the file
            with open(self.file_path, 'w') as file:
                # Dump the configuration to the file, disabling the default flow style
                yaml.dump(self.config, file, default_flow_style=False)
                # print("Configuration updated successfully")
        except Exception as e:
            # Handle errors that may occur during the writing process
            print(f"Error writing to file: {e}")


class Staticconfig(Config):
    def __init__(self):
        super().__init__('staticconfig.yml')


# class Dynamicconfig(Config):
#     def __init__(self):
#         super().__init__('dynamicconfig.yml')



