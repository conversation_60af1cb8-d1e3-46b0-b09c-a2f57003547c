# set base image 
FROM python:3.11.0

# set the working directory in the container
WORKDIR /app

# Set the time zone to IST
ENV TZ=Asia/Kolkata

# Update the system clock
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# copy the dependencies file to the working directory
COPY requirement.txt .

# install dependencies
RUN pip install -r requirement.txt

# copy the content of the local src directory to the working directory
COPY . .

# Copy the .env file into the container
COPY .env .

# Set environment variables
ENV AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
ENV AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
ENV AWS_STORAGE_BUCKET_NAME=$AWS_STORAGE_BUCKET_NAME
ENV AWS_S3_REGION_NAME=$AWS_S3_REGION_NAME

# command to run on container start
CMD [ "python", "runrtu.py" ]