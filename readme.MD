### **RTU Pre-processing**
A Rule Base engine that Identifies, labels, and segregates to respective designated categories (Anomaly, Spike, and Alerts) the data of IOT driven Energy solution developed by Applied Solar technologies.

### **Project Flow**
1.	Retrieve all Day counts specific to HPSC Category (RTUID = [2081,2158] ,ProjectID = [21,25]) with reference to receive date from Database and store it as List. Limits to validate the condition tree are retieved from config, if not availbale then used default. (Note: 1)
2.	Extract and Process the Data for each sent-date stored in the list. (Note: 2)
3.	Feature engineering is implemented. Creating Downtime, Total system run hrs, EB run hrs, BB run hrs, PM(Note :3)
4.	feed the site-wise data which is sorted based on the sent date to the condition tree and label the feeds .
5.	Generate Formatted summary and Data.
6.	Send mail to Teams.
7.  Store the data into the MySQL.

---
**Note:**
1.	Specifically, in HPSC there Is a provision for Log data. Log Data is data stored locally in the device itself in case the site is not communicating with the server (NONCOMM). Once the communication is re-established, all stored data is pushed to the server (LIFO). Hence while extracting data on the bases of received data (Date when data is received in the Database) there are multiple sent dates (Date generated by the devices)
2.	Engine run for N-1 day. N-1 day means for the previous day (Date Specific).
3.	Downtime: calculates the total time the site has MCBFail Alarm for a day. TotalSysRunHrs: the difference between the value of TotalSysRunHrs last feed and first feed with respect to sentdate.
EBRunHrsNew: the difference between the value of EBRunHrsNew last feed and the first feed with respect to sentdate. 
BBRunHrs: the difference between the value of BBRunHrs last feed and first feed with respect to sentdate.


---
| MetaIndex | Description                                                                                                                                                                                                                                                                                                  |
| --------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| A1        | EBAvail is False when any of R,Y or B rectifier phase voltages are in the normal range (The normal range is  100V to 285V) .                                                                                                                                                                                 |
| A2        | EBAvail is True when all of R, Y or B rectifier phase voltages are in the abnormal range or no voltage is available (The abnormal range <100V and >285V) and Smoke Alarm is False.                                                                                                                           |
| B1        | EBAvail is False and the battery is charging.                                                                                                                                                                                                                                                                |
| B2        | Battery Charging and DisCharging simultaneously                                                                                                                                                                                                                                                              |
| B3        | MCBFailAlarm is True (MCB turned off) and EBAvail is True (Grid is available), the battery charging current = 0 Amps and discharging current = 0 Amps.                                                                                                                                                       |
| B4        | MCBFailAlarm is True (MCB turned off), EBAvail is False (Grid is not available), battery run hours is less than 4 hours,LVD voltage base alarm is False, the LVD SOC base alarm is False, and the battery charging current = 0 Amps and discharging current = 0 Amps.LVD Alarm is coupled with MCBFailAlarm. |
| B5        | MCBFailAlarm is False (MCB turned On), EBAvail is False (Grid is not available), system run hours are less than 24 hours, battery run hour is less than 4 hours, and the battery charging  = 0 Amp and battery discharging = 0 Amp.                                                                          |
| C1        | (Total System Current - Load Current - Battery charging Current + Battery discharging Current ) ~ 2.5 and EBAvail is True (Grid available)                                                                                                                                                                   |
| C2        | (Total System Current - Load Current - Battery charging Current + Battery discharging Current ) ~ 2.5 and EBAvail is False (Grid not available)                                                                                                                                                              |
| D1        | HighTempAlarm is False, cabinet temperature is 55 degrees or above                                                                                                                                                                                                                                           |
| D2        | HighTempAlarm is now False, HighTempAlarm was once high and the cabinet temperature is still 50 degrees or above.                                                                                                                                                                                            |
| D3        | HighTempAlarm is still True, and cabinet temperature has reached below 50 degrees.                                                                                                                                                                                                                           |
| D4        | HighTempAlarm is True,  and cabinet temperature has not reached 55 degrees.                                                                                                                                                                                                                                  |
| E1        | MBDAlarm is True before battery discharge greater than 3Amps for the duration of less than 15 mins , when smoke Alarm is False and EBAvail is True.                                                                                                                                                          |
| E2        | MBDAlarm is False after battery discharge greater than 3 Amps for more than 15 mins, when smoke Alarm is  False and EBAvail is True.                                                                                                                                                                         |
| E3        | MBDAlarm is False before charging current greater than 1 amps when EBAvail is True or EBAvail is False, when smoke Alarm is False.                                                                                                                                                                           |
| E4        | MBDAlarm is True when EBAvail is False (it is an alarm deactivation condition)., when smoke Alarm is False .                                                                                                                                                                                                 |
| E5        | MBDAlarm is True after charging current greater than 1 amps and EBAvail is True, when smoke Alarm is False.                                                                                                                                                                                                  |
| F1        | HighVoltAlarm is False,  battery voltage is 58 volts or above                                                                                                                                                                                                                                                |
| F2        | HighVoltAlarm is now False, HighVoltAlarm was earlier True and the battery voltage is still 56 volts or above.                                                                                                                                                                                               |
| F3        | HighVoltAlarm is still True,  but battery voltage has dropped below 56 volts.                                                                                                                                                                                                                                |
| F4        | HighVoltAlarm is True, battery voltage is less than 58 volts.                                                                                                                                                                                                                                                |
| G1        | LowVoltAlarm is False,  battery voltage is 46 volts or below                                                                                                                                                                                                                                                 |
| G2        | LowVoltAlarm is now False, LowVoltAlarm was earlier True and the battery voltage is still less than 48 volts.                                                                                                                                                                                                |
| G3        | LowVoltAlarm is True, but battery voltage has reached 48 volts or above.                                                                                                                                                                                                                                     |
| G4        | LowVoltAlarm is True, and battery voltage is greather than 46 volts.                                                                                                                                                                                                                                         |
| G5        | LowVoltAlarm is False,  battery voltage is 46 volts or below, dgonalarm is false                                                                                                                                                                                                                             |
| G6        | LowVoltAlarm is now False, LowVoltAlarm was earlier True and the battery voltage is still less than 48 volts, dgonalarm is false                                                                                                                                                                             |
| G7        | LowVoltAlarm is True, but battery voltage has reached 48 volts or above, dgonalarm is false                                                                                                                                                                                                                  |
| G8        | LowVoltAlarm is True, and battery voltage is greather than 46 volts, dgonalarm is false                                                                                                                                                                                                                      |
| H1        | LowVoltPLDAlarm is False, battery voltage is 44.5 volts or below                                                                                                                                                                                                                                             |
| H2        | LowVoltPLDAlarm is now False, LowVoltPLDAlarm was earlier True and the battery voltage is still lesser than 47 volts.                                                                                                                                                                                        |
| H3        | LowVoltPLDAlarm is still True,  but battery voltage has reached 47 volts or above.                                                                                                                                                                                                                           |
| H4        | LowVoltPLDAlarm is True,  battery voltage is greater than 44.5 volts.                                                                                                                                                                                                                                        |
| H5        | LowVoltPLDAlarm is False, battery voltage is 44.5 volts or below, dgonalarm is false                                                                                                                                                                                                                         |
| H6        | LowVoltPLDAlarm is now False, LowVoltPLDAlarm was earlier True and the battery voltage is still lesser than 47 volts, dgonalarm is false                                                                                                                                                                     |
| H7        | LowVoltPLDAlarm is still True,  but battery voltage has reached 47 volts or above, dgonalarm is false                                                                                                                                                                                                        |
| H8        | LowVoltPLDAlarm is True,  battery voltage is greater than 44.5 volts, dgonalarm is false                                                                                                                                                                                                                     |
| I1        | LVD1 is True, EBAvail is False, Battery voltage is greater than or equal to 42 volts.                                                                                                                                                                                                                        |
| I2        | LVD1 is True, EBAvail is True.                                                                                                                                                                                                                                                                               |
| I3        | LVD1 is False , EBAvail is False, Battery Voltage is below 42 volts.                                                                                                                                                                                                                                         |
| I4        | LVD1 is True, EBAvail is False, Battery voltage is greater than or equal to 42 volts, dgonalarm is false                                                                                                                                                                                                     |
| I5        | LVD1 is True, EBAvail is True, dgonalarm is false                                                                                                                                                                                                                                                            |
| I6        | LVD1 is False , EBAvail is False, Battery Voltage is below 42 volts, dgonalarm is false                                                                                                                                                                                                                      |
| J1        | LowSOCLVDAlarm is True, EBAvail is False, Battery SOC is greater than or equal to 20%.                                                                                                                                                                                                                       |
| J2        | LowSOCLVDAlarm is True, EBAvail is True.                                                                                                                                                                                                                                                                     |
| J3        | LowSOCLVDAlarm is False, Battery SOC is below 20%.                                                                                                                                                                                                                                                           |
| J4        | LowSOCLVDAlarm is True, EBAvail is False, Battery SOC is greater than or equal to 20%, dgonalarm is false                                                                                                                                                                                                    |
| J5        | LowSOCLVDAlarm is True, EBAvail is True, dgonalarm is false                                                                                                                                                                                                                                                  |
| J6        | LowSOCLVDAlarm is False, Battery SOC is below 20%, dgonalarm is false                                                                                                                                                                                                                                        |
| K1        | LowSOCAlarm is False,  battery SOC is 25% or below                                                                                                                                                                                                                                                           |
| K2        | LowSOCAlarm is now False,  LowSOCAlarm was True and the battery SOC is still 27% or below.                                                                                                                                                                                                                   |
| K3        | LowSOCAlarm is still True,  battery SOC has reached above 27%.                                                                                                                                                                                                                                               |
| K4        | LowSOCAlarm is True, battery SOC is above 25%.                                                                                                                                                                                                                                                               |
| K5        | LowSOCAlarm is False,  battery SOC is 25% or below, dgonalarm is false                                                                                                                                                                                                                                       |
| K6        | LowSOCAlarm is now False,  LowSOCAlarm was True and the battery SOC is still 27% or below, dgonalarm is false                                                                                                                                                                                                |
| K7        | LowSOCAlarm is still True,  battery SOC has reached above 27%, dgonalarm is false                                                                                                                                                                                                                            |
| K8        | LowSOCAlarm is True, battery SOC is above 25%, dgonalarm is false                                                                                                                                                                                                                                            |
| L1        | LowSOCPLDAlarm is False, EBAvail is False, battery SOC is 22% or below                                                                                                                                                                                                                                       |
| L2        | LowSOCPLDAlarm is now False, LowSOCPLDAlarm was earlier True and the battery SOC is still 25% or below.                                                                                                                                                                                                      |
| L3        | LowSOCPLDAlarm is still True, but battery SOC has reached above 25%.                                                                                                                                                                                                                                         |
| L4        | LowSOCPLDAlarm is True, battery SOC is greater than 22%.                                                                                                                                                                                                                                                     |
| L5        | LowSOCPLDAlarm is False, EBAvail is False, battery SOC is 22% or below, dgonalarm is false                                                                                                                                                                                                                   |
| L6        | LowSOCPLDAlarm is now False, LowSOCPLDAlarm was earlier True and the battery SOC is still 25% or below, dgonalarm is false                                                                                                                                                                                   |
| L7        | LowSOCPLDAlarm is still True, but battery SOC has reached above 25%, dgonalarm is false                                                                                                                                                                                                                      |
| L8        | LowSOCPLDAlarm is True, battery SOC is greater than 22%, dgonalarm is false                                                                                                                                                                                                                                  |
| M1        | System Run Hours are not equal to 24hrs.                                                                                                                                                                                                                                                                     |
| M2        | System Run Hours is Negative                                                                                                                                                                                                                                                                                 |
| M3        | System Run Hours is more than 24hrs                                                                                                                                                                                                                                                                          |
| N1        | EBMainsAbnormalAlarm False,  EBPhaseRVoltRect above 285 volts.                                                                                                                                                                                                                                               |
| N2        | EBMainsAbnormalAlarm is False,  EBPhaseRVoltRect above 265 volts.                                                                                                                                                                                                                                            |
| N3        | EBMainsAbnormalAlarm is True, EBPhaseRVoltRect between 100 volts and 265 volts.                                                                                                                                                                                                                              |
| N4        | EBMainsAbnormalAlarm is True,  EBPhaseRVoltRect less than 285 volts.                                                                                                                                                                                                                                         |
| N5        | EBMainsAbnormalAlarm is False, EBPhaseRVoltRect above 30 volts and below 100 volts.                                                                                                                                                                                                                          |
| N6        | EBMainsAbnormalAlarm is False, EBMainsAbnormalAlarm was True, EBPhaseRVoltRect above 30 volts and below 120 volts.                                                                                                                                                                                           |
| N7        | EBMainsAbnormalAlarm is True,  EBPhaseRVoltRect between 120 volts to 285 volts.                                                                                                                                                                                                                              |
| N8        | EBMainsAbnormalAlarm is True, EBPhaseRVoltRect greater than 100 volts.                                                                                                                                                                                                                                       |
| N9        | EBMainsAbnormalAlarm is True, EBPhaseRVoltRect in 120 volts to 265 volts.                                                                                                                                                                                                                                    |
| N10       | EBMainsAbnormalAlarm is True, EBPhaseRVoltRect less than 30 volts.                                                                                                                                                                                                                                           |
| O1        | EBMainsAbnormalAlarm is False, EBPhaseYVoltRect greater than 285 volts.                                                                                                                                                                                                                                      |
| O2        | EBMainsAbnormalAlarm is False, EBMainsAbnormalAlarm was True, EBPhaseYVoltRect greater than 265 volts.                                                                                                                                                                                                       |
| O3        | EBMainsAbnormalAlarm is True, EBPhaseYVoltRect between 100 volts to 265 volts.                                                                                                                                                                                                                               |
| O4        | EBMainsAbnormalAlarm is True, EBPhaseYVoltRect less than 285 volts.                                                                                                                                                                                                                                          |
| O5        | EBMainsAbnormalAlarm is False, EBPhaseYVoltRect above 30 volts and below 100 volts.                                                                                                                                                                                                                          |
| O6        | EBMainsAbnormalAlarm is False,  EBPhaseYVoltRect above 30 volts and below 120 volts.                                                                                                                                                                                                                         |
| O7        | EBMainsAbnormalAlarm is True, EBPhaseYVoltRect between 120 volts to 285 volts.                                                                                                                                                                                                                               |
| O8        | EBMainsAbnormalAlarm is True, EBPhaseYVoltRect greater than 100 volts.                                                                                                                                                                                                                                       |
| O9        | EBMainsAbnormalAlarm is True, EBPhaseYVoltRect between 120 volts to 265 volts.                                                                                                                                                                                                                               |
| O10       | EBMainsAbnormalAlarm is True, EBPhaseYVoltRect is less than 30 volts.                                                                                                                                                                                                                                        |
| P1        | EBMainsAbnormalAlarm is False, EBPhaseBVoltRect greater than 285 volts.                                                                                                                                                                                                                                      |
| P2        | EBMainsAbnormalAlarm is False, EBMainsAbnormalAlarm was True, EBPhaseBVoltRect greater than 265 volts.                                                                                                                                                                                                       |
| P3        | EBMainsAbnormalAlarm is True, EBPhaseBVoltRect between 100 volts to 265 volts.                                                                                                                                                                                                                               |
| P4        | EBMainsAbnormalAlarm is True, EBPhaseBVoltRect less than 285 volts.                                                                                                                                                                                                                                          |
| P5        | EBMainsAbnormalAlarm is False, EBPhaseBVoltRect above 30 volts and below 100 volts.                                                                                                                                                                                                                          |
| P6        | EBMainsAbnormalAlarm is False,  EBPhaseBVoltRect above 30 volts and below 120 volts.                                                                                                                                                                                                                         |
| P7        | EBMainsAbnormalAlarm is True, EBPhaseBVoltRect between 120 volts to 285 volts.                                                                                                                                                                                                                               |
| P8        | EBMainsAbnormalAlarm is True, EBPhaseBVoltRect greater than 100 volts.                                                                                                                                                                                                                                       |
| P9        | EBMainsAbnormalAlarm is True, EBPhaseBVoltRect between 120 volts to 265 volts.                                                                                                                                                                                                                               |
| P10       | EBMainsAbnormalAlarm is True, EBPhaseBVoltRect is less than 30 volts.                                                                                                                                                                                                                                        |
| Q1        | BattComFailAlarm is True, SOC or SOH are greater than 0.                                                                                                                                                                                                                                                     |
| R1        | RectFailAlarm is False when  EBPhaseYVoltRect is in normal range and EBPhaseRVoltRect is less than 30 Volts                                                                                                                                                                                                  |
| R2        | RectFailAlarm is False when  EBPhaseRVoltRect is in normal range and EBPhaseYVoltRect is less than 30 Volts                                                                                                                                                                                                  |
| R3        | RectFailAlarm is True when  EBPhaseRVoltRect and EBPhaseYVoltRect both are above 30 Volts.                                                                                                                                                                                                                   |
| S1        | DCPeakCurrent if greater 20 Amp(Need to be rest)                                                                                                                                                                                                                                                             |
| T1        | Missing DataPackets                                                                                                                                                                                                                                                                                          |
| U1        | DGAvail is True and EBAvail is True simultaneously is not possible                                                                                                                                                                                                                                           |
| Rst1      | Reset Duration Alert                                                                                                                                                                                                                                                                                         |
| Rst2      | Reset Indicator                                                                                                                                                                                                                                                                                              |