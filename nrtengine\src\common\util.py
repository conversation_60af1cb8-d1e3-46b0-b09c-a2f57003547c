import os
import shutil
from datetime import datetime ,timedelta
from src.util.accessconfig import (Staticconfig )

class DelLog:
    def __init__(self , file_name):
        self.file_name = file_name
        self.delete_log()
        
    def delete_log(self) -> None:
        if os.path.exists(self.file_name):
            try:
                if os.path.isfile(self.file_name):
                    os.remove(self.file_name)  # Use os.remove for files
                elif os.path.isdir(self.file_name):
                    shutil.rmtree(self.file_name)  # Use shutil.rmtree for directories
                print(f"Deleted: {self.file_name}")
            except Exception as e:
                print(f"Error deleting '{self.file_name}': {e}")
        else:
            print("Not found")