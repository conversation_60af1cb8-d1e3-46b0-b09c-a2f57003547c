import boto3
import logging
import os
import pandas as pd 
from botocore.exceptions import NoCredentialsError, ClientError
from datetime import timedel<PERSON>, datetime
from src.util.accessconfig import (Staticconfig )

class S3FileHandler:
    def __init__(self, Dynamicconfig: dict):
        self.shandler = Staticconfig()  
        self.bucket_name = self.shandler.get_value(['s3', 'bucketname'])
        self.s3_client = boto3.client('s3')
        self.dynamicconfig = Dynamicconfig

    def upload_file(self):
        try:
            current_date = pd.to_datetime(self.dynamicconfig['rundate']).strftime(r'%Y-%m-%d')
            log_filename = f"{current_date}-{self.dynamicconfig['rtu']}.log"
            local_log_file = f"{log_filename}"
            self.s3_client.upload_file(local_log_file, self.bucket_name, log_filename, ExtraArgs={'ACL': 'public-read'})
            print(f"File {log_filename} uploaded to S3 bucket")
        except NoCredentialsError:
            print('IAM role-based credentials not found. Make sure your code is running on an AWS resource with the appropriate IAM role.')
        except Exception as e:
            print(f'Error uploading file to S3: {e}')

    def check_log_exists(self, log_filename):
        try:
            self.s3_client.head_object(Bucket=self.bucket_name, Key=log_filename)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            else:
                raise e

    def download_file(self, log_filename, local_log_file):
        try:
            self.s3_client.download_file(self.bucket_name, log_filename, local_log_file)
            print(f"File {log_filename} downloaded from S3")
        except Exception as e:
            print(f"Error downloading file from S3: {e}")

    def logger(self):
        # Creating an object
        current_date = pd.to_datetime(self.dynamicconfig['rundate']).strftime(r'%Y-%m-%d')
        log_filename = f"{current_date}-{self.dynamicconfig['rtu']}.log"
        local_log_file = f"{log_filename}"
        log = logging.getLogger(__name__)
        log.setLevel(logging.INFO)

        # Create a file handler to log to the specified file
        file_handler = logging.FileHandler(filename=local_log_file)
        formatter = logging.Formatter('%(asctime)s : %(levelname)s : %(message)s')
        file_handler.setFormatter(formatter)
        log.addHandler(file_handler) # Add the handler to the logger

        return log

    def log_file_create(self):
        current_date = pd.to_datetime(self.dynamicconfig['rundate']).strftime(r'%Y-%m-%d')
        log_filename = f"{current_date}-{self.dynamicconfig['rtu']}.log"

        # Local path for the log file (e.g., /tmp/2024-09-13.log)
        local_log_file = f"{log_filename}"

        # Check if the log file exists in S3
        if self.check_log_exists(log_filename):
            # Download the log file from S3
            self.download_file(log_filename, local_log_file)
        else:
            # Create a new log file
            open(local_log_file, 'w').close()  # Create an empty log file

    def delete_files(self):
        try:
            current_date =  pd.to_datetime(self.dynamicconfig['rundate'])
            # Create a list of file names for the last 7 to 14 days
            keep_files = [
                f"{(current_date - timedelta(days=7)).strftime('%Y-%m-%d')}-{d}.log"
                for d in [2081,2158,2370]
            ]

            # List objects in the S3 bucket
            response = self.s3_client.list_objects_v2(Bucket=self.bucket_name)

            # Check each file in the bucket
            for obj in response.get('Contents', []):
                obj_key = obj['Key']
                if obj_key.endswith('.log'):
                    file_name = obj_key.split('/')[-1]
                    if file_name in keep_files:
                        self.s3_client.delete_object(Bucket=self.bucket_name, Key=obj_key)
                        logging.info(f"Deleted {obj_key}")
                    
        except Exception as e:
            logging.error(f"Error deleting files: {e}")


