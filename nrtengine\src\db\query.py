import pandas as pd
import traceback
import time
import logging
from src.db.databaseconnection import (MSSQLManager, MYSQLManager)
from src.util.accessconfig import Staticconfig
from datetime import datetime ,timedelta

class MSSQLQuery:
    """
    Class to handle MSSQL queries and retrieve dataframes.
    """
    def __init__(self) -> None:
        """
        Initialize MSSQLQuery.

        Retrieves configuration, initializes the connection, and sets the day attribute.
        """
        self.shandler = Staticconfig()
        self.msmanager = MSSQLManager()
        self.conn = self.msmanager.conn

    def getdataframe(self, sqlquery: str) -> pd.DataFrame:
        """
        Execute an MSSQL query and return the result as a DataFrame.

        Parameters:
        - sqlquery (str): SQL query to be executed.

        Returns:
        - pd.DataFrame: Result of the query as a DataFrame.
        """
        try:
            data = pd.read_sql_query(sqlquery, self.conn)
            if not data.empty:
                logging.info("getdataframe build successfully")
            else:
                logging.info("empty dataframe")
            return data
        except Exception as error:
            logging.error(f"MSSQLQuery getdataframe: {error}")

    def getdictionary(self, sqlquery: str) -> dict:
        """
        Execute an MSSQL query and return the result as a dictionary.

        Parameters:
        - sqlquery (str): SQL query to be executed.

        Returns:
        - dictionary or None: Result of the query as a dictionary.
        """
        # Create a cursor
        cursor = self.conn.cursor(as_dict= True)

        # Execute the query
        cursor.execute(sqlquery)

        # Fetch the result as a dictionary
        try: 
            result = cursor.fetchall()[0]
        except Exception as error:
            result = {}

        # Close the cursor
        cursor.close()

        return result

    def getlist(self, sqlquery: str) -> list:
        """
        Execute an MYSQL query and return the result as a list.

        Parameters:
        - sqlquery (str): SQL query to be executed.

        Returns:
        - list or None: Result of the query as a list.
        """
        # Create a cursor
        cursor = self.conn.cursor()

        # Execute the query
        cursor.execute(sqlquery)

        # Fetch the result as a tuple
        try:
            result = cursor.fetchall()
        except :
            result = None
       
        # Close the cursor
        cursor.close()

        return result
    

    def getvalue(self, sqlquery: str) -> tuple:
        """
        Execute an MSSQL query and return the result as a tuple.

        Parameters:
        - sqlquery (str): SQL query to be executed.

        Returns:
        - tuple or None: Result of the query as a tuple.
        """
        # Create a cursor
        cursor = self.conn.cursor()

        # Execute the query
        cursor.execute(sqlquery)

        # Fetch the result as a dictionary
        try:
            result = cursor.fetchone()
        except :
            result = None
       
        # Close the cursor
        cursor.close()

        return result
    
    

    def rawdata(self, Dynamicconfig:dict,rtu:int) -> pd.DataFrame:
        """
        Retrieve and return an rawdata dataframe from a predefined SQL query.

        Returns:
        - dataframe: rawdata dataframe.
        """
        features = self.shandler.get_value([Dynamicconfig['rtuname'],'features'])
        sqlquery =f"""SELECT {features}
        FROM ASTTelecom.dbo.tblSiteDataO1 AS tbl1 WITH (NOLOCK)
        JOIN ASTTelecom.dbo.tblSite AS tbl2 WITH (NOLOCK) ON tbl1.Site_Id = tbl2.Id
        WHERE tbl2.RTUType = {rtu}
        AND tbl2.IsActive = 1
        AND SentDate BETWEEN '{Dynamicconfig['runtime']}' AND '{Dynamicconfig['currenttime']}';"""

        data = self.getdataframe(sqlquery)

        if not data.empty:
            logging.info('rawdata build successfully')
        else:
            logging.info('Check rawdata:')

        return data
    
    def lmudata(self, Dynamicconfig:dict) -> pd.DataFrame:
        """
        Retrieve and return an rawdata dataframe from a predefined SQL query.

        Returns:
        - dataframe: lmudata dataframe.
        """
        sqlquery =f"""SELECT tbl1.SentDate, tbl1.BattV, tbl1.loadCurrent
        FROM ASTTelecom.dbo.tblSiteDataO1 AS tbl1 WITH (NOLOCK) WHERE 
        tbl1.Site_Id = {Dynamicconfig['siteid']} AND
        tbl1.SentDate BETWEEN '{Dynamicconfig['dayrange']}' AND '{Dynamicconfig['currenttime']}' ORDER BY tbl1.SentDate;"""

        data = self.getdataframe(sqlquery)

        if not data.empty:
            logging.info('lmudata build successfully')
        else:
            logging.info('Check lmudata:')

        return data
    
    def capacity(self, siteid: str) -> float:
        
        sqlquery = f"""select sum(CM.Size) from tblSiteEquipmentMaster Sm with (nolock) 
                inner join AST_Common.dbo.tblitemcapacitymapping ICM  with (nolock)  on ICM.Id=SM.Capacity_Id
                and ICM.ItemMaster_Id=SM.Equipment_Type_Id and ICM.IsActive=1
                inner join AST_Common.dbo.tblCapacityMaster CM  with (nolock) on CM.Id=ICM.Capacity_Id and CM.IsActive=1
                where  SM.Equipment_Type_Id = 25 and SM.status='Y' and SM.Site_Id = {siteid} ;"""

        data = self.getvalue(sqlquery)

        return data


    def alarmopen(self, Dynamicconfig:dict) -> pd.DataFrame:
        """
        Retrieve and return an alarm list from a predefined SQL query.

        Returns:
        - dataframe: rawdata dataframe.

        and AlarmDesc in ({alarm_list})
        """
        # alarm_list = ",".join(f"'{alarm}'" for alarm in self.shandler.get_value(['alarms']))
        sqlquery =f"""select AlarmDesc from ASTTelecom.dbo.tblNOCOpenAlarm with(nolock)
                    where Site_Id = {Dynamicconfig['siteid']} and Status  = 'Y' ;"""

        data = [x[0] for x in self.getlist(sqlquery)]

        return data
 
    
    def configdata(self,Dynamicconfig: dict) -> None:
        """
        Retrieve dictionary and update configdata from a predefined SQL query.

        Returns:
        - None
        """
        sqlquery =f"""SELECT  top 1 timebasealarmsettingminute,hightempalarmdegree,lowvoltagealarmsettingvolt,pldcutoffalarmsettingvolt,pldrestoresettingvolt,lvdcutoffalarmsettingvolt,lvdsoccuttoffsettingpercent,lowsocsettingpercent,pldsoccuttoffsettingpercent,pldsocrestoresettingpercent,mainsabnormallowalarmvolt,mainsabnormalhighalarmvolt FROM 
        (SELECT Site_Id,CreatedOn,timebasealarmsettingminute,hightempalarmdegree,lowvoltagealarmsettingvolt,pldcutoffalarmsettingvolt,pldrestoresettingvolt,lvdcutoffalarmsettingvolt,lvdsoccuttoffsettingpercent,lowsocsettingpercent,pldsoccuttoffsettingpercent,pldsocrestoresettingpercent,mainsabnormallowalarmvolt,mainsabnormalhighalarmvolt,ROW_NUMBER() OVER (PARTITION BY Site_id ORDER BY CreatedOn DESC) AS RN 
        FROM ASTTelecom.dbo.tblSiteConfigurationData WITH (NOLOCK) ) subquery
        WHERE RN = 1 
        AND Site_Id = {Dynamicconfig['siteid']}
        ORDER BY CreatedOn desc;"""

        data = self.getdictionary(sqlquery)
        
        try:
            data = {key: float(value) for key, value in data.items()} if data else {}
        except:
            data = {}

        return data
    
    # def ttno(self,siteid,alarm) -> bool:
    #     """
    #     Retrieve a tuple and return a bool dgcheck from a predefined SQL query.

    #     Returns:
    #     - bool: dgcheck bool.
    #     """
    #     sqlquery =f"""select TTNo from tblNOCAlarm with(Nolock) WHERE AlarmDesc = '{alarm}' AND Site_Id = {siteid} ;"""

    #     data = self.getvalue(sqlquery)

    #     return data
    
    def makercheck(self, siteid) -> bool:
        
        query = f"""select Site_Id from tblSiteEquipmentMaster with(nolock) where Equipment_Type_Id = 29 and Equipment_Make_Id in (35,195) and STATUS = 'Y' and Site_Id = {siteid};"""

        data = self.getvalue(query)
        
        try:
            return bool(data[0])
        except :
            return False

    def spalarm(self, body) -> bool:
        """
        Retrieve a tuple and return a bool dgcheck from a predefined SQL query.

        Returns:
        - bool: dgcheck bool.
        """

        # Create a cursor object to interact with the database
        cursor = self.conn.cursor()
        query = f"""EXEC Usp_ParserFeedAlarm_Update {body["Site_Id"]}, {body["RTUType"]}, '{body["FeedAlarmStatus"]}','A1', '{body["Receiveddate"]}', 0,null, '{body["Threshold"]}'""" 
        # Execute the stored procedure
        cursor.execute(query)

        # Commit the transaction if necessary (depending on your stored procedure)
        self.conn.commit()

        # Close the cursor and connection
        cursor.close()
        

    def customerid(self,siteid) -> bool:
        """
        Retrieve a tuple and return a bool dgcheck from a predefined SQL query.

        Returns:
        - bool: dgcheck bool.
        """
        sqlquery =f"""select CustomerId from tblSite with(nolock) where Id = {siteid} ;"""

        data = self.getvalue(sqlquery)

        return data
    
    def batterycapacity(self,siteid) ->str:
        sqlquery = f'''with cte as (select eqp.Site_Id,ts.CustomerSiteId,ts.CustomerId,eqp.Equipment_InstallationDate,
        case 
            when cmaster_code.Size is not null then cmaster_code.Size
            when cmaster.Size is not null then cmaster.Size
            when (cmaster_code.Size is null and cmaster.Size is null) then eqp.capacity
            End as capacity,
        eqp.Qty
        from ASTTelecom.dbo.tblSiteEquipmentMaster eqp with(nolock)
        inner join ASTTelecom.dbo.tblSite ts with(nolock) on ts.Id=eqp.Site_Id
        left join AST_Common.dbo.tblItemCapacityMapping cmap with(nolock) on cmap.id=eqp.Capacity_Id
        left join AST_Common.dbo.tblCapacityMaster cmaster with(nolock) on cmaster.Id=cmap.Capacity_Id
        left join AST_Common.dbo.tblItemEquipmentDetail eqp_detail with(nolock) on eqp.Equipment_CodeId_Id=eqp_detail.id
        left join AST_Common.dbo.tblCapacityMaster cmaster_code with(nolock) on eqp_detail.Capacity=cmaster_code.id
        where ts.IsActive=1
        and eqp.Equipment_Type_Id=4
        and STATUS='Y'
        and ts.id = {siteid})
        select sum(capacity*Qty) as capacity 
        from cte
        where capacity is not null
        and Equipment_InstallationDate is not null
        group by Site_Id'''

        data = self.getvalue(sqlquery)

        try:
            return data[0]
        except:
            return None
        

    def circlescheck(self , siteid) -> int:
        """
        Retrieve a tuple and return a bool dgcheck from a predefined SQL query.
        """
        sqlquery = f"""SELECT CircleId FROM ASTTelecom.dbo.tblSite WITH (NOLOCK) WHERE Id = {siteid};"""

        data = self.getvalue(sqlquery)

        try:   
            return data[0]
        except:
            return None

        
        
    
    def dgcheck(self,siteid) -> bool:
        """
        Retrieve a tuple and return a bool dgcheck from a predefined SQL query.

        Returns:
        - bool: dgcheck bool.
        """
        sqlquery =f"""SELECT dgstatus FROM ASTTelecom.dbo.tblSite WITH (NOLOCK) WHERE id = {siteid};"""

        data = self.getvalue(sqlquery)
        try:
            if data[0].lower() == 'y':
                return True 
            else:
                return False
        except:
            return False
        

        
    def BatteryCheck(self, siteid:str) -> int:
        """
        Retrieve a tuple and update batterytype from a predefined SQL query.

        Returns:
        - None
        """
        sqlquery =f"""SELECT DISTINCT Equipment_Category_Id FROM ASTTelecom.dbo.tblSiteEquipmentMaster WITH (NOLOCK) 
        WHERE Site_Id = {siteid} 
        AND Equipment_Category_Id IS NOT NULL
        AND Equipment_Type_Id = 4
        AND STATUS = 'Y';"""

        data = self.getvalue(sqlquery)
    
        try:
            return int(data[0])
        except:
            return None
        

    def SiteClassification(self,siteid:str) -> None:
        """
        Extract the Site Classification store the output to the w.r.t. the classification for NON NLD, BSC & hub the function with return 1 else 0 if function passes null 
        then none into the dynamicconfiguration.

        return 
        1 : NON-NLD 
        0: NLD 
        None : When the Site lassification is Null
        """
        
        sqlquery = f"""SELECT CASE WHEN definition_name LIKE '%NLD%' THEN 0
                    ELSE 1
                    END 
                    FROM ASTTelecom.dbo.tblDefinitionV1 WITH (NOLOCK)
                    WHERE id = (SELECT DISTINCT SiteClassification 
                    FROM ASTTelecom.dbo.tblSite WITH (NOLOCK)
                    WHERE IsActive = 1 AND SiteClassification IS NOT NULL AND id = {siteid});
                    """
        data = self.getvalue(sqlquery)
        try:
            return int(data[0])
        except:
            return None
        
            
class MYSQLQuery:
    """
    Class to handle MySQL queries and retrieve dataframes.
    """
    def __init__(self) -> None:
        """
        Initialize MYSQLQuery.

        Retrieves configuration, initializes the connection, and sets the day attribute.
        """
       
        self.shandler = Staticconfig()
        self.mymanager = MYSQLManager()
        self.conn = self.mymanager.conn

    def getdataframe(self, sqlquery: str) -> pd.DataFrame:
        """
        Execute an MYSQL query and return the result as a DataFrame.

        Parameters:
        - sqlquery (str): SQL query to be executed.

        Returns:
        - pd.DataFrame: Result of the query as a DataFrame.
        """
        try:
            data = pd.read_sql_query(sqlquery, self.conn)
            if not data.empty:
                logging.info("getdataframe build successfully")
            else:
                logging.info("empty dataframe")
            return data
        except Exception as error:
            logging.error(f"MYSQLQuery getdataframe: {error}")

    def getlist(self, sqlquery: str) -> list:
        """
        Execute an MYSQL query and return the result as a list.

        Parameters:
        - sqlquery (str): SQL query to be executed.

        Returns:
        - list or None: Result of the query as a list.
        """
        # Create a cursor
        cursor = self.conn.cursor()

        # Execute the query
        cursor.execute(sqlquery)

        # Fetch the result as a tuple
        try:
            result = cursor.fetchall()
        except :
            result = None
       
        # Close the cursor
        cursor.close()

        return result
    

    def getvalue(self, sqlquery: str) -> tuple:
        """
        Execute an MYSQL query and return the result as a tuple.

        Parameters:
        - sqlquery (str): SQL query to be executed.

        Returns:
        - tuple or None: Result of the query as a tuple.
        """
        # Create a cursor
        cursor = self.conn.cursor()

        # Execute the query
        cursor.execute(sqlquery)

        # Fetch the result as a tuple
        try:
            result = cursor.fetchone()
        except :
            result = None
       
        # Close the cursor
        cursor.close()

        return result

    

    def sqldump(self,dumpdata: pd.DataFrame, Dynamicconfig:dict) -> None:
        """
        Perform a bulk insert or update operation on a MySQL table based on the primary key.

        Parameters:
        - dumpdata (pd.DataFrame): A Pandas DataFrame containing the data to be inserted or updated.

        Returns:
        None
        """
        try:
            # Open a cursor for database interaction
            cursor = self.conn.cursor()
            # dumpdata[['Site_Id','SentDate','EBVoltage','BattDisChgA','Issues','AnomalyMeta']].to_excel("test1.xlsx")
            # Get the minimum SendDate value from the DataFrame
            min_time = dumpdata['SentDate'].min() + timedelta(minutes=60)
            # Filter the DataFrame to only include rows with a SendDate value equal to or greater than the minimum SendDate value
            dumpdata = dumpdata[dumpdata['SentDate'] >= min_time][self.shandler.get_value([Dynamicconfig['rtuname'], 'dump'])]
            # Convert DataFrame to dictionary and replace np.nan with None
            data_dict = dumpdata.to_dict(orient='records')
            data_dict = [{key: (value if not pd.isna(value) else None) for key, value in entry.items()} for entry in data_dict]
            # Create multiple tuples, each representing a row in the DataFrame
            data = [tuple(entry.values()) for entry in data_dict]
            # Extract column names and create placeholders for the SQL query
            columns = ', '.join(dumpdata.columns)
            placeholders = ', '.join(['%s'] * len(dumpdata.columns))
            # Construct the SQL query for bulk insert or update
            query = f"INSERT INTO ASTTelecom_ArchiveFromApril2022.{Dynamicconfig['rtuname'].lower()}anomalydata_nrt ({columns}) VALUES ({placeholders}) ON DUPLICATE KEY UPDATE "
            query += ', '.join([f"{column} = VALUES({column})" for column in dumpdata.columns])
            # Construct the SQL query for bulk insert or update
            # cursor.executemany(query, data)
            for i in range(0, len(data), 20):
                cursor.executemany(query, data[i:i + 20])
            # Commit the changes to the database
            self.conn.commit()
            # Close the database cursor
            cursor.close()
        except Exception as error :
            # Print an error message if an exception occurs during the operation
            print("MySQL Issue during dump:", error)
            # Close the database cursor
            cursor.close()
            pass

    def alarmdump(self,dumpdata: pd.DataFrame) -> None:
        """
        Perform a bulk insert or update operation on a MySQL table based on the primary key.

        Parameters:
        - dumpdata (pd.DataFrame): A Pandas DataFrame containing the data to be inserted or updated.

        Returns:
        None
        """
        try:
            # Open a cursor for database interaction
            cursor = self.conn.cursor()
            # Get the minimum SendDate value from the DataFrame
            data_dict = dumpdata.to_dict(orient='records')
            data_dict = [{key: (value if not pd.isna(value) and value != 'nan' and value != 'None' else None) for key, value in entry.items()} for entry in data_dict]
            # Create multiple tuples, each representing a row in the DataFrame
            data = [tuple(entry.values()) for entry in data_dict]
            # Extract column names and create placeholders for the SQL query
            columns = ', '.join(dumpdata.columns)
            placeholders = ', '.join(['%s'] * len(dumpdata.columns))
            # Construct the SQL query for bulk insert or update
            query = f"INSERT INTO ASTTelecom_ArchiveFromApril2022.nrtalarm ({columns}) VALUES ({placeholders}) ON DUPLICATE KEY UPDATE "
            query += ', '.join([f"{column} = VALUES({column})" for column in dumpdata.columns])
            # Construct the SQL query for bulk insert or update
            cursor.executemany(query, data)
            # Commit the changes to the database
            self.conn.commit()
            # Close the database cursor
            cursor.close()
        except Exception as error :
            # Print an error message if an exception occurs during the operation
            print("MySQL Issue during dump:", error )
            # Close the database cursor
            cursor.close()
            pass
    
    def deletenoalarm(self) -> None:
        """
        Delete rows from the 'nrtalarm' table where columns A3, A4, A5, and A6 are all NULL.
        
        Returns:
        None
        """
        
        try:
            # Open a cursor for database interaction
            cursor = self.conn.cursor()
            alarm_list = " AND ".join(f"{alarm} IS NULL" for alarm in self.shandler.get_value(['alarms']))
            # Delete rows where A3, A4, A5, and A6 are NULL
            delete_query = f"""
            DELETE FROM ASTTelecom_ArchiveFromApril2022.nrtalarm
            WHERE {alarm_list};
            """
            # EB_LV IS NULL AND EB_HV IS NULL AND RMFAIL IS NULL AND HVOLT IS NULL AND LMUF IS NULL
            cursor.execute(delete_query)

            # Commit the changes to the database
            self.conn.commit()

        except Exception as error:
            # Print an error message if an exception occurs during the operation
            print("MySQL Issue during delete:", error )

        finally:
            # Close the database cursor if it was opened
            if cursor is not None:
                cursor.close()


    def alarmdata(self) -> None:
        """
        Fetches MetaIndex data from the database and updates it in the data handler.

        The MetaIndex is retrieved from the 'tblRtuMaster' table in the ASTTelecom_ArchiveFromApril2022 database.

        :return: None
        """
        # SQL query to retrieve MetaIndex from the database
        sql_query = "SELECT * FROM ASTTelecom_ArchiveFromApril2022.nrtalarm;"

        data = self.getdataframe(sql_query)

        if not data.empty:
            logging.info('rawdata build successfully')
        else:
            logging.info('Check rawdata:')

        return data
 
    
    
    def metaindex(self) -> None:
        """
        Fetches MetaIndex data from the database and updates it in the data handler.

        The MetaIndex is retrieved from the 'tblRtuMaster' table in the ASTTelecom_ArchiveFromApril2022 database.

        :return: None
        """
        # SQL query to retrieve MetaIndex from the database
        sql_query = "SELECT MetaIndex FROM ASTTelecom_ArchiveFromApril2022.tblRtuMaster;"

        # Fetching data from the database using the defined SQL query
        data =  [row[0] for row in self.getlist(sql_query)]

        return data

       