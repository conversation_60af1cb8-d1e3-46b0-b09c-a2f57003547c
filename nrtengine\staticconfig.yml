CRMU:
  header: CRMU
  bool:
  - EBAvail
  - CSUConnect
  - DoorAlarm
  - DCMCOMFailAlarm
  - SmokeAlarm
  - HighTempAlarm
  - LowVoltAlarm
  - LMUAlarm
  - BattComFailAlarm
  - LMUFailAlarm
  - LIBOneComFailAlarm
  - LIBTwoComFailAlarm
  - CE_Delta_PP
  - CE_VNT24KW_PP
  - CE_VNT7KW_PP
  - TVI_Delta_PP
  datetime64:
  - SentDate
  - ReceivedDate
  dump: 
  - Id
  - Site_Id
  - FirwareVersion
  - SentDate
  - ReceivedDate
  - AnomalyNumber
  - Issues
  - AnomalyMeta
  - SpikeNumber
  - SpikeIssues
  - SpikeMeta
  - AlertNumber
  - Alert
  - AlertMeta
  features: tbl1.Id,tbl2.CustomerSiteId,tbl2.SiteId as DeviceId,tbl1.Site_Id,tbl1.FirwareVersion,tbl1.DataIdentifier,tbl1.FeedType,tbl1.SentDate,tbl1.CreationDate
    AS ReceivedDate,tbl1.CSUConnect,tbl1.BattV,tbl1.BattChgA,tbl1.BattDisChgA,tbl1.Temp,tbl1.EBVoltage,tbl1.EBPhaseRVolt,tbl1.EBPhaseYVolt,tbl1.EBPhaseBVolt,tbl1.EBPhaseRVoltRect,tbl1.EBPhaseYVoltRect,tbl1.EBPhaseBVoltRect,tbl1.CHOneCurent,tbl1.CHOneKWH,tbl1.CHOnePeakCurrent,tbl1.CHOneAvgCurrent,tbl1.CHTwoCurent,tbl1.CHTwoKWH,tbl1.CHThreeCurent,tbl1.CHThreeKWH,tbl1.CHFourCurent,tbl1.CHFourKWH,tbl1.loadCurrent,tbl1.EBRunHrsNew,tbl1.BBRunHrs,tbl1.SystemrunHRs,tbl1.SOC,tbl1.SOH,tbl1.Charge_KWH,tbl1.DisCharge_KWH,tbl1.DataPacketCount,tbl1.LMUFailAlarm,tbl1.LMUAlarm,tbl1.BatteryCount,tbl1.LIBOneCycleCount,tbl1.LIBOneDischgCycleCount,tbl1.LIBOneCurrent,tbl1.LIBOneTemp,tbl1.LIBOneVoltage,tbl1.LIBOneSOC,tbl1.LIBOneSOH,tbl1.LIBTwoCycleCount,tbl1.LIBTwoDischgCycleCount,tbl1.LIBTwoCurrent,tbl1.LIBTwoTemp,tbl1.LIBTwoVoltage,tbl1.LIBTwoSOC,tbl1.LIBTwoSOH,tbl1.EBAvail,tbl1.DCMCOMFailAlarm,tbl1.HighTempAlarm,tbl1.LowVoltAlarm,tbl1.BattComFailAlarm,tbl1.DoorAlarm,tbl1.SmokeAlarm,tbl1.LIBOneComFailAlarm,tbl1.LIBTwoComFailAlarm,tbl1.CE_Delta_PP,tbl1.CE_VNT24KW_PP,tbl1.CE_VNT7KW_PP,tbl1.TVI_Delta_PP
  float64:
  - BattV
  - BattChgA
  - BattDisChgA
  - Temp
  - EBPhaseRVolt
  - EBPhaseYVolt
  - EBPhaseBVolt
  - EBPhaseRVoltRect
  - EBPhaseYVoltRect
  - EBPhaseBVoltRect
  - EBRunHrsNew
  - EBVoltage
  - BBRunHrs
  - SystemrunHRs
  - CHOneCurent
  - CHOneKWH
  - CHOnePeakCurrent
  - CHOneAvgCurrent
  - CHTwoCurent
  - CHTwoKWH
  - CHThreeCurent
  - CHThreeKWH
  - CHFourCurent
  - CHFourKWH
  - loadCurrent
  - SOC
  - SOH
  - Charge_KWH
  - DisCharge_KWH
  - DataPacketCount
  - BatteryCount
  - LIBOneCycleCount
  - LIBOneDischgCycleCount
  - LIBOneCurrent
  - LIBOneTemp
  - LIBOneVoltage
  - LIBOneSOC
  - LIBOneSOH
  - LIBTwoCycleCount
  - LIBTwoDischgCycleCount
  - LIBTwoCurrent
  - LIBTwoTemp
  - LIBTwoVoltage
  - LIBTwoSOC
  - LIBTwoSOH
  rangefeatures:
  - BBRunHrs
  - EBRunHrsNew
  - SystemrunHRs
  - Charge_KWH
  - DisCharge_KWH
  str:
  - Id
  - DeviceId
  - CustomerSiteId
  - Site_Id
  - DataIdentifier
  - FirwareVersion
  - FeedType
default:
  hightempalarmdegree: 55
  highvoltalarmvolt: 58
  lowsocsettingpercent: 25
  lowvoltagealarmsettingvolt: 46
  lvdcutoffalarmsettingvolt: 42
  lvdsoccuttoffsettingpercent: 20
  mainsabnormalhighalarmvolt: 285
  mainsabnormallowalarmvolt: 100
  pldcutoffalarmsettingvolt: 44.5
  pldrestoresettingvolt: 48
  pldsoccuttoffsettingpercent: 22
  pldsocrestoresettingpercent: 25
  safevoltage: 30
  timebasealarmsettingminute: 240
ecolite:
  header: EcoLite(Diesel Assist High Power Small Cell)
  bool:
  - EBAvail
  - DGONAvail
  - SmokeAlarm
  - CSUConnect
  - HighTempAlarm
  - HRTConnect
  - MBDAlarm
  - LVD1
  - HighVoltAlarm
  - LowVoltAlarm
  - LowVoltPLDAlarm
  - LowSOCAlarm
  - LowSOCPLDAlarm
  - LowLVDSOCAlarm
  - EBMainsAbnormalAlarm
  - SystemOverloadAlarm
  - TempCompensationVRLAAlarm
  - BattComFailAlarm
  - SPDFailAlarm
  - RectInputOffAlarm
  - RectFailAlarm
  - HexFailAlarm
  - DCMCOMFailAlarm
  - RectInternalFaultAlarm
  - FanOnAlarm
  - BackupTimeLapsCutOff
  - CE_Delta_PP
  - CE_VNT24KW_PP
  - CE_VNT7KW_PP
  - TVI_Delta_PP
  datetime64:
  - SentDate
  - ReceivedDate
  dump:
  - Id 
  - Site_Id 
  - ReceivedDate 
  - SentDate 
  - FirwareVersion 
  - AnomalyNumber 
  - Issues 
  - AnomalyMeta 
  - SpikeNumber 
  - SpikeIssues 
  - SpikeMeta 
  - AlertNumber 
  - Alert 
  - AlertMeta
  features: tbl1.Id,tbl2.CustomerSiteId,tbl2.SiteId as DeviceId,tbl1.Site_Id,tbl1.CreationDate AS ReceivedDate,tbl1.SentDate,tbl1.DataIdentifier,tbl1.BattV,tbl1.BattChgA,tbl1.BattDisChgA,tbl1.Temp,tbl1.EBPhaseRVolt,tbl1.EBPhaseYVolt,tbl1.EBPhaseBVolt,tbl1.EBPhaseRVoltACEM,tbl1.EBPhaseYVoltACEM,tbl1.EBPhaseBVoltACEM,tbl1.EBPhaseRVoltRect,tbl1.EBPhaseYVoltRect,tbl1.EBPhaseBVoltRect,tbl1.EBRunHrsNew,tbl1.loadCurrent,tbl1.TotalSystemCurrent,tbl1.DCLoadPeakCurrent,tbl1.DCLoadPeakPower,tbl1.DCLoadKWH,tbl1.BBRunHrs,tbl1.SystemrunHRs,tbl1.EBKWH,tbl1.EBPhaseRcurrent,tbl1.EBPhaseBcurrent,tbl1.EBPhaseYcurrent,tbl1.EBPhaseRcurrentRect,tbl1.EBPhaseYcurrentRect,tbl1.EBPhaseBcurrentRect,tbl1.CHOneCurent,tbl1.CHOneKWH,tbl1.CHTwoCurent,tbl1.CHTwoKWH,tbl1.CHThreeCurent,tbl1.CHThreeKWH,tbl1.CHFourCurent,tbl1.CHFourKWH,tbl1.DGPhaseRVolt,tbl1.DGPhaseYVolt,tbl1.DGPhaseBVolt,tbl1.DGRunHrsNew,tbl1.SOC,tbl1.SOH,tbl1.Charge_KWH,tbl1.DisCharge_KWH,tbl1.DataPacketCount,tbl1.FirwareVersion,tbl1.EBAvail,tbl1.DGONAvail,tbl1.SmokeAlarm,tbl1.CSUConnect,tbl1.HighTempAlarm,tbl1.HRTConnect,tbl1.MBDAlarm,tbl1.LVD1,tbl1.HighVoltAlarm,tbl1.LowVoltAlarm,tbl1.LowVoltPLDAlarm,tbl1.LowSOCAlarm,tbl1.LowSOCPLDAlarm,tbl1.LowLVDSOCAlarm,tbl1.EBMainsAbnormalAlarm,tbl1.SystemOverloadAlarm,tbl1.TempCompensationVRLAAlarm,tbl1.BattComFailAlarm,tbl1.SPDFailAlarm,tbl1.DCMCOMFailAlarm,tbl1.RectInputOffAlarm,tbl1.RectFailAlarm,tbl1.HexFailAlarm,tbl1.BackupTimeLapsCutOff,tbl1.RectInternalFaultAlarm,tbl1.FanOnAlarm,tbl1.CE_Delta_PP,tbl1.CE_VNT24KW_PP,tbl1.CE_VNT7KW_PP,tbl1.TVI_Delta_PP
  float64:
  - BattV
  - BattChgA
  - BattDisChgA
  - Temp
  - EBPhaseRVolt
  - EBPhaseYVolt
  - EBPhaseBVolt
  - EBPhaseRVoltACEM
  - EBPhaseYVoltACEM
  - EBPhaseBVoltACEM
  - EBPhaseRVoltRect
  - EBPhaseYVoltRect
  - EBPhaseBVoltRect
  - DGPhaseRVolt
  - DGPhaseYVolt
  - DGPhaseBVolt
  - DGRunHrsNew
  - EBRunHrsNew
  - loadCurrent
  - TotalSystemCurrent
  - DCLoadPeakCurrent
  - DCLoadPeakPower
  - DCLoadKWH
  - BBRunHrs
  - SystemrunHRs
  - EBKWH
  - EBPhaseRcurrent
  - EBPhaseBcurrent
  - EBPhaseYcurrent
  - EBPhaseRcurrentRect
  - EBPhaseYcurrentRect
  - EBPhaseBcurrentRect
  - SOC
  - SOH
  - Charge_KWH
  - DisCharge_KWH
  - DataPacketCount
  - CHOneCurent
  - CHOneKWH
  - CHTwoCurent
  - CHTwoKWH
  - CHThreeCurent
  - CHThreeKWH
  - CHFourCurent
  - CHFourKWH
  rangefeatures:
  - BBRunHrs
  - EBRunHrsNew
  - DGRunHrsNew
  - SystemrunHRs
  - Charge_KWH
  - DisCharge_KWH
  - DCLoadKWH
  str:
  - Id
  - DeviceId
  - CustomerSiteId
  - Site_Id
  - DataIdentifier
  - FirwareVersion
features:
  alert: Alert
  alertmeta: AlertMeta
  alertnumber: AlertNumber
  anomalymeta: AnomalyMeta
  anomalynumber: AnomalyNumber
  backuptimelapscutoff: BackupTimeLapsCutOff
  battchga: BattChgA
  battcomfailalarm: BattComFailAlarm
  battdischga: BattDisChgA
  batterycount: BatteryCount
  battv: BattV
  bbrunhrs: BBRunHrs
  ce_delta_pp: CE_Delta_PP
  ce_vnt24kw_pp: CE_VNT24KW_PP
  ce_vnt7kw_pp: CE_VNT7KW_PP
  charge_kwh: Charge_KWH
  chfourcurent: CHFourCurent
  chfourkwh: CHFourKWH
  choneavgcurrent: CHOneAvgCurrent
  chonecurent: CHOneCurent
  chonekwh: CHOneKWH
  chonepeakcurrent: CHOnePeakCurrent
  chthreecurent: CHThreeCurent
  chthreekwh: CHThreeKWH
  chtwocurent: CHTwoCurent
  chtwokwh: CHTwoKWH
  csuconnect: CSUConnect
  customersiteid: CustomerSiteId
  dataidentifier: DataIdentifier
  datapacketcount: DataPacketCount
  dcloadkwh: DCLoadKWH
  dcloadpeakcurrent: DCLoadPeakCurrent
  dcloadpeakpower: DCLoadPeakPower
  dcmcomfailalarm: DCMCOMFailAlarm
  deviceid: DeviceId
  dgonavail: DGONAvail
  dgphasebvolt: DGPhaseBVolt
  dgphaservolt: DGPhaseRVolt
  dgphaseyvolt: DGPhaseYVolt
  dgrunhrsnew: DGRunHrsNew
  discharge_kwh: DisCharge_KWH
  downtime: DownTime
  ebavail: EBAvail
  ebkwh: EBKWH
  ebmainsabnormalalarm: EBMainsAbnormalAlarm
  ebphasebcurrent: EBPhaseBcurrent
  ebphasebcurrentrect: EBPhaseBcurrentRect
  ebphasebvolt: EBPhaseBVolt
  ebphasebvoltacem: EBPhaseBVoltACEM
  ebphasebvoltrect: EBPhaseBVoltRect
  ebphasercurrent: EBPhaseRcurrent
  ebphasercurrentrect: EBPhaseRcurrentRect
  ebphaservolt: EBPhaseRVolt
  ebphaservoltacem: EBPhaseRVoltACEM
  ebphaservoltrect: EBPhaseRVoltRect
  ebphaseycurrent: EBPhaseYcurrent
  ebphaseycurrentrect: EBPhaseYcurrentRect
  ebphaseyvolt: EBPhaseYVolt
  ebphaseyvoltacem: EBPhaseYVoltACEM
  ebphaseyvoltrect: EBPhaseYVoltRect
  ebrunhrsnew: EBRunHrsNew
  ebvoltage: EBVoltage
  fanonalarm: FanOnAlarm
  feedtype: FeedType
  firwareversion: FirwareVersion
  hexfailalarm: HexFailAlarm
  hightempalarm: HighTempAlarm
  highvoltalarm: HighVoltAlarm
  hrtconnect: HRTConnect
  id: Id
  issues: Issues
  libonecomfailalarm: LIBOneComFailAlarm
  libonecurrent: LIBOneCurrent
  libonecyclecount: LIBOneCycleCount
  libonedischgcyclecount: LIBOneDischgCycleCount
  libonesoc: LIBOneSOC
  libonesoh: LIBOneSOH
  libonetemp: LIBOneTemp
  libonevoltage: LIBOneVoltage
  libtwocomfailalarm: LIBTwoComFailAlarm
  libtwocurrent: LIBTwoCurrent
  libtwocyclecount: LIBTwoCycleCount
  libtwodischgcyclecount: LIBTwoDischgCycleCount
  libtwosoc: LIBTwoSOC
  libtwosoh: LIBTwoSOH
  libtwotemp: LIBTwoTemp
  libtwovoltage: LIBTwoVoltage
  lmualarm: LMUAlarm
  lmufailalarm: LMUFailAlarm
  loadcurrent: loadCurrent
  lowlvdsocalarm: LowLVDSOCAlarm
  lowsocalarm: LowSOCAlarm
  lowsocpldalarm: LowSOCPLDAlarm
  lowvoltalarm: LowVoltAlarm
  lowvoltpldalarm: LowVoltPLDAlarm
  lvd1: LVD1
  mbdalarm: MBDAlarm
  pm: PM
  receiveddate: ReceivedDate
  rectfailalarm: RectFailAlarm
  rectinputoffalarm: RectInputOffAlarm
  rectinternalfaultalarm: RectInternalFaultAlarm
  sentdate: SentDate
  site_id: Site_Id
  smokealarm: SmokeAlarm
  soc: SOC
  soh: SOH
  spdfailalarm: SPDFailAlarm
  spikeissues: SpikeIssues
  spikemeta: SpikeMeta
  spikenumber: SpikeNumber
  systemcounter: SystemCounter
  systemoverloadalarm: SystemOverloadAlarm
  systemrunhrs: SystemrunHRs
  temp: Temp
  tempcompensationvrlaalarm: TempCompensationVRLAAlarm
  totalsystemcurrent: TotalSystemCurrent
  tvi_delta_pp: TVI_Delta_PP
hpsc:
  header: HPSC(High Power Small Cell)
  bool:
  - EBAvail
  - SmokeAlarm
  - CSUConnect
  - HighTempAlarm
  - HRTConnect
  - MBDAlarm
  - LVD1
  - HighVoltAlarm
  - LowVoltAlarm
  - LowVoltPLDAlarm
  - LowSOCAlarm
  - LowSOCPLDAlarm
  - LowLVDSOCAlarm
  - EBMainsAbnormalAlarm
  - SystemOverloadAlarm
  - TempCompensationVRLAAlarm
  - BattComFailAlarm
  - SPDFailAlarm
  - RectInputOffAlarm
  - RectFailAlarm
  - HexFailAlarm
  - RectInternalFaultAlarm
  - FanOnAlarm
  - BackupTimeLapsCutOff
  - CE_Delta_PP
  - CE_VNT24KW_PP
  - CE_VNT7KW_PP
  - TVI_Delta_PP
  datetime64:
  - SentDate
  - ReceivedDate
  dump:
  - Id 
  - Site_Id 
  - ReceivedDate 
  - SentDate 
  - FirwareVersion 
  - AnomalyNumber 
  - Issues 
  - AnomalyMeta 
  - SpikeNumber 
  - SpikeIssues 
  - SpikeMeta 
  - AlertNumber 
  - Alert 
  - AlertMeta

  features: tbl1.Id,tbl2.CustomerSiteId,tbl2.SiteId as DeviceId,tbl1.Site_Id,tbl1.CreationDate AS ReceivedDate,tbl1.SentDate,tbl1.DataIdentifier,tbl1.BattV,tbl1.BattChgA,tbl1.BattDisChgA,tbl1.Temp,tbl1.EBPhaseRVolt,tbl1.EBPhaseYVolt,tbl1.EBPhaseBVolt,tbl1.EBPhaseRVoltACEM,tbl1.EBPhaseYVoltACEM,tbl1.EBPhaseBVoltACEM,tbl1.EBPhaseRVoltRect,tbl1.EBPhaseYVoltRect,tbl1.EBPhaseBVoltRect,tbl1.EBRunHrsNew,tbl1.loadCurrent,tbl1.TotalSystemCurrent,tbl1.DCLoadPeakCurrent,tbl1.DCLoadPeakPower,tbl1.DCLoadKWH,tbl1.BBRunHrs,tbl1.SystemrunHRs,tbl1.EBKWH,tbl1.EBPhaseRcurrent,tbl1.EBPhaseBcurrent,tbl1.EBPhaseYcurrent,tbl1.EBPhaseRcurrentRect,tbl1.EBPhaseYcurrentRect,tbl1.EBPhaseBcurrentRect,tbl1.SOC,tbl1.SOH,tbl1.Charge_KWH,tbl1.DisCharge_KWH,tbl1.DataPacketCount,tbl1.FirwareVersion,tbl1.EBAvail,tbl1.SmokeAlarm,tbl1.CSUConnect,tbl1.HighTempAlarm,tbl1.HRTConnect,tbl1.MBDAlarm,tbl1.LVD1,tbl1.HighVoltAlarm,tbl1.LowVoltAlarm,tbl1.LowVoltPLDAlarm,tbl1.LowSOCAlarm,tbl1.LowSOCPLDAlarm,tbl1.LowLVDSOCAlarm,tbl1.EBMainsAbnormalAlarm,tbl1.SystemOverloadAlarm,tbl1.TempCompensationVRLAAlarm,tbl1.BattComFailAlarm,tbl1.SPDFailAlarm,tbl1.RectInputOffAlarm,tbl1.RectFailAlarm,tbl1.HexFailAlarm,tbl1.BackupTimeLapsCutOff,tbl1.RectInternalFaultAlarm,tbl1.FanOnAlarm,tbl1.CE_Delta_PP,tbl1.CE_VNT24KW_PP,tbl1.CE_VNT7KW_PP,tbl1.TVI_Delta_PP
  float64:
  - BattV
  - BattChgA
  - BattDisChgA
  - Temp
  - EBPhaseRVolt
  - EBPhaseYVolt
  - EBPhaseBVolt
  - EBPhaseRVoltACEM
  - EBPhaseYVoltACEM
  - EBPhaseBVoltACEM
  - EBPhaseRVoltRect
  - EBPhaseYVoltRect
  - EBPhaseBVoltRect
  - EBRunHrsNew
  - loadCurrent
  - TotalSystemCurrent
  - DCLoadPeakCurrent
  - DCLoadPeakPower
  - DCLoadKWH
  - BBRunHrs
  - SystemrunHRs
  - EBKWH
  - EBPhaseRcurrent
  - EBPhaseBcurrent
  - EBPhaseYcurrent
  - EBPhaseRcurrentRect
  - EBPhaseYcurrentRect
  - EBPhaseBcurrentRect
  - SOC
  - SOH
  - Charge_KWH
  - DisCharge_KWH
  - DataPacketCount
  rangefeatures:
  - BBRunHrs
  - EBRunHrsNew
  - SystemrunHRs
  - Charge_KWH
  - DisCharge_KWH
  - DCLoadKWH
  str:
  - Id
  - DeviceId
  - CustomerSiteId
  - Site_Id
  - DataIdentifier
  - FirwareVersion
index:
  1: 26
  2: 43
limit:
  battchga: 2
  battdischga: 4
  bbcurrenteqnlimit: 2.5
  ebavailhigh: 285
  ebavaillow: 100
  ebcurrenteqnlimit: 2.5
  hightempalarmlimit: 5
  highvoltalarmlimit: 2
  lowsocsettingpercentlimit: 2
  lowvoltagealarmsettinglimit: 2
  mainsabnormalhighalarmvoltlimit: 10
  mainsabnormallowalarmvoltlimit: 15
  mbdchglimit: 1
  mbddischglimit: 3
  mbdtimelimit: 900
  pvhigh: 60
  pvlow: 30
  rmu_hightempalarmlimit: 3
mailer:
  emailid: <EMAIL>
  password: '@st@pplied2'
  recipientemailid: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
rtutype:
  2081: hpsc
  2158: ecolite
  2370: CRMU

alarms: 
  - BHCR
  - EB_LV
  - EB_HV
  - RMFAIL
  - DCM
  - SMPSO
  - CTRFail

nrtalarm: 
  - siteid
  - EB_LV
  - EB_HV
  - RMFAIL
  - HVOLT
  - LMUF
  - DCM
  - BHCR
  - SMPSO
  - CTRFail
  
s3:
  bucketname: rtu-anomaly
  filename: summary_{date}.xlsx
  archivename: archive_{date}.pkl
  summary: ./data/{date}/{rtu}_summary_{date}.xlsx
  url: https://rtu-anomaly.s3.ap-south-1.amazonaws.com/{rtu}_summary_{date}.xlsx
ticket: https://api.astnoc.com/ASTTelecomWebAPI/api/CMS/ComplaintCreation
save:
  excelsummary: ./data/{date}/{rtu}_summary_{date}.xlsx
  pkldata: ./data/{date}/{rtu}_archive_{date}.pkl
  foldername: ./data/{date}
  mailtable: ./data/mailtable.txt
openurl: https://api.astnoc.com/ASTTelecomWebAPI/api/Alarm/CreateAlarm
closeurl: https://api.astnoc.com/ASTTelecomWebAPI/api/Alarm/Close

