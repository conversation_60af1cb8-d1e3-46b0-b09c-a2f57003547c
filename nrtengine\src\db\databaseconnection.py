import os
import json
import logging
from dotenv import load_dotenv
from pymysql import connect as mysql
from pymssql import connect as mssql
from sqlalchemy import create_engine
load_dotenv()

class DatabaseConnector:
    """
    Singleton class for managing database connections.
    """

    conn = None
    # instance = None

    # def __new__(cls, *args, **kwargs):
        # """
        # Singleton pattern: Ensures only one instance is created.
        # """
        # if cls.instance is None:
        #     cls.instance = object.__new__(cls)
        # return cls.instance
    
    def __init__(self, config_section):
        """
        Initialize DatabaseConnector.

        Parameters:
        - config_section (str): The configuration section for the database (e.g., 'mssql' or 'mysql').
        """
        try:
            if self.conn is None:
                data = json.loads(os.getenv(config_section))
                # Read connection details from configuration file
                self.server = data['server']
                self.database = data['database']
                self.username = data['username']
                self.password = data['password']

                # Check the database type and create the appropriate connection
                if config_section == 'mssql':
                    # Use pymssql for MSSQL connection
                    self.conn = mssql(server=self.server, user=self.username, password=self.password, database=self.database, autocommit=True)
                elif config_section == 'mysql':
                    # Use pymysql for MySQL connection
                    self.conn = mysql(host=self.server, user=self.username, password=self.password, database=self.database, autocommit=True)
                
                logging.info(f"{config_section.capitalize()} has been connected successfully")
            else:
                logging.info(f"{config_section.capitalize()} connected")
        except Exception as e:
            logging.error(f"{config_section.capitalize()} Connection Manager: {e}")

class MSSQLManager(DatabaseConnector):
    """
    Manager class for MSSQL database connection.
    """
    def __init__(self):
        # Initialize the parent class with the 'mssql' configuration section
        super().__init__('mssql')

class MYSQLManager(DatabaseConnector):
    """
    Manager class for MySQL database connection.
    """
    def __init__(self):
        # Initialize the parent class with the 'mysql' configuration section
        super().__init__('mysql')


class MYSQLEngine:
    def __init__(self) -> None:
        data = json.loads(os.getenv('mysql'))
        self.server = data['server']
        self.database = data['database']
        self.username = data['username']
        self.password = data['password']
        self.engine = create_engine(f"mysql+pymysql://{self.username}:{self.password }@{self.server}/{self.database}")
    


