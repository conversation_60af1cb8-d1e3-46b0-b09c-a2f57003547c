import warnings
from datetime import datetime ,timedelta
from src.util.accessconfig import Staticconfig 
from src.db.query import (MSSQLQuery , MYSQLQuery)
from src.models.model import Detect
# from src.common.util import Utility
from src.preprocess.datapreprocess import Preprocess
# from concurrent.futures import Thr<PERSON><PERSON><PERSON><PERSON>xecutor
from multiprocessing import Pool
from concurrent.futures import Process<PERSON>oolExecutor
from copy import deepcopy
from tqdm import tqdm
from src.common.s3aws import S3FileHandler
from src.common.util import DelLog
import pandas as pd
import time
import logging


warnings.filterwarnings('ignore')  

if __name__ == "__main__":

    
    
    config= {"batterytype": None,
        "capacitycheck": None,
        "day": None,
        "metaindex": None,
        "rtuname": None,
        "rundate": None,
        "sigma": None,
        "siteconfig": None,
        "siteid": None,
        "soccheck": None,
        "siteclassification": None,
        "batterycapacity": None,
        "circleid": None,
        "customerid":None,
        "pm":None,
        "dg": None,
        "alarms": None,
        "lastdaydata":None}
    

    shandler = Staticconfig()
    
    
    mysqlquery = MYSQLQuery()
    config['metaindex'] = mysqlquery.metaindex()
    mysqlquery.conn.close()
    current_time = datetime.now()
    # current_time = datetime.strptime("2024-01-10 12:00:00", r"%Y-%m-%d %H:%M:%S")
    todaydate = current_time.date()
    config['rundate']=todaydate
    config['day'] = 1
    config['currenttime'] = current_time.strftime(r"%Y/%m/%d %H:%M:%S")
    runtime = current_time - timedelta(hours=3)
    config['runtime'] = runtime.strftime(r"%Y/%m/%d %H:%M:%S")
    dayrange = current_time - timedelta(hours=24 ,minutes= 20)
    config['dayrange'] = dayrange.strftime(r"%Y/%m/%d %H:%M:%S")
    Dynamicconfig = config.copy()

    

    def process_site_data(batch):
        local_alarm = pd.DataFrame(columns=shandler.get_value(['nrtalarm']))
        for data in batch:
            mssql_query_obj = MSSQLQuery()
            mysql_query_obj = MYSQLQuery()
            siteid = data['siteid']
            Dynamicconfig = deepcopy(data['Dynamicconfig'])
            rtu = Dynamicconfig['rtu']
            Dynamicconfig['siteid'] = str(siteid)
            Dynamicconfig['batterytype'] = mssql_query_obj.BatteryCheck(siteid = Dynamicconfig['siteid'])
            Dynamicconfig['siteclassification'] = mssql_query_obj.SiteClassification(siteid = Dynamicconfig['siteid'])
            Dynamicconfig['customerid'] = mssql_query_obj.customerid(siteid = Dynamicconfig['siteid'])
            Dynamicconfig['circleid'] = mssql_query_obj.circlescheck(siteid = Dynamicconfig['siteid'])
            Dynamicconfig['siteconfig'] = mssql_query_obj.configdata(Dynamicconfig = Dynamicconfig)
            Dynamicconfig['capacity'] = mssql_query_obj.capacity(siteid = Dynamicconfig['siteid'])
            Dynamicconfig['alarms'] = mssql_query_obj.alarmopen(Dynamicconfig = Dynamicconfig)
            Dynamicconfig['makercheck'] = mssql_query_obj.makercheck(siteid = Dynamicconfig['siteid'])
            Dynamicconfig['batterycapacity'] = mssql_query_obj.batterycapacity(siteid = Dynamicconfig['siteid'])
            if rtu == 2158:
                Dynamicconfig["dg"]=mssql_query_obj.dgcheck(siteid =Dynamicconfig['siteid'])
            rawdata = data['groupeddata'].copy()
            preprocessobj = Preprocess(Dynamicconfig=Dynamicconfig,rawdata=rawdata)
            unlabeldata = preprocessobj.processdata().copy()
            database = data['database'].copy()
            detectobj = Detect(Dynamicconfig=Dynamicconfig,data=unlabeldata,database = database, mssqlquery = mssql_query_obj, log = log )
            delivery , alarmdata = detectobj.loop()[:]
            
            mysql_query_obj.sqldump(dumpdata = delivery,Dynamicconfig=Dynamicconfig)
            if alarmdata.shape[0] > 0:
                local_alarm = pd.concat([local_alarm,alarmdata],ignore_index= True)
                
            
            mssql_query_obj.conn.close()
            mysql_query_obj.conn.close()
            del mssql_query_obj,mysql_query_obj,unlabeldata,preprocessobj,rawdata,Dynamicconfig,detectobj,delivery,database

        return local_alarm

    
    for rtu in [2081,2158,2370]:    
        rtuname = shandler.get_value(['rtutype'])
        # make alarm  data from 
        alarm = pd.DataFrame(columns=shandler.get_value(['nrtalarm']))
        if rtu in rtuname.keys():
            Dynamicconfig['rtuname'] = rtuname[rtu]
            Dynamicconfig['rtu'] = rtu
            handler = S3FileHandler(Dynamicconfig)
            handler.log_file_create()
            log = handler.logger()
            mssqlquery = MSSQLQuery()
            fulldata = mssqlquery.rawdata(Dynamicconfig=Dynamicconfig,rtu = rtu)
            mssqlquery.conn.close()
            mysqlquery = MYSQLQuery()
            database = mysqlquery.alarmdata()
            mysqlquery.conn.close()
            
            fulldata['Site_Id'] = fulldata['Site_Id'].astype(int)
            groupeddata = fulldata.groupby(shandler.get_value(['features', 'site_id']))
            temp_site_list = [{'siteid':s, 'database': database ,'Dynamicconfig': Dynamicconfig , 'groupeddata' : groupeddata.get_group(s) } for s in fulldata.Site_Id.unique()]
            # Use multiprocessing for parallel site processing
            
            batch_size = max(1, len(temp_site_list) // 4)  # Divide sites into batches
            site_batches = [temp_site_list[i:i + batch_size] for i in range(0, len(temp_site_list), batch_size)]
            del temp_site_list
            
            with ProcessPoolExecutor(max_workers=4) as executor:
                results = list(tqdm(executor.map(process_site_data, site_batches), total=len(site_batches)))

            alarm = pd.concat(results, ignore_index=True)
            mysql_query_obj = MYSQLQuery()
            mysql_query_obj.alarmdump(dumpdata = alarm)
            mysql_query_obj.deletenoalarm()
            mysql_query_obj.conn.close()
            handler.upload_file()
            handler.delete_files()
            del handler, groupeddata, fulldata, log ,batch_size, site_batches, mysql_query_obj
        DelLog(f"{Dynamicconfig['rundate'].strftime('%Y-%m-%d')}-{Dynamicconfig['rtu']}.log")