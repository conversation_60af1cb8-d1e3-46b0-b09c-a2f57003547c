import pandas as pd
from src.util.accessconfig import (Staticconfig )



class Preprocess:
    def __init__(self,Dynamicconfig:dict ,rawdata: pd.DataFrame):
        """
        Initialize Preprocess.
        Retrieves configuration, initializes the necessary attributes, and retrieves raw data from MSSQL.
        """
        # `self.mssqlobject` is creating an instance of the `MSSQLQuery` class. This allows the
        # `Preprocess` class to access methods and attributes defined in the `MSSQLQuery` class. By
        # creating this instance, the `Preprocess` class can interact with the database using the
        # methods provided by the `MSSQLQuery` class, such as `rawdata` and `PMCheck`.
        
        self.shandler = Staticconfig()
        self.dynamicconfig = Dynamicconfig
        self.rtuname = self.dynamicconfig['rtuname']
        self.rangefeatures = self.shandler.get_value([self.rtuname, 'rangefeatures'])
        self.boolean = self.shandler.get_value([self.rtuname, 'bool'])
        self.string = self.shandler.get_value([self.rtuname, 'str'])
        self.numerical = self.shandler.get_value([self.rtuname, 'float64'])
        self.datetime = self.shandler.get_value([self.rtuname, 'datetime64'])
        self.data = rawdata

    def processdata(self) -> pd.DataFrame: 
        """
        Process raw data and return a preprocessed DataFrame.
        Returns:
        - pd.DataFrame: Preprocessed DataFrame.
        """
        self.df = self.data.copy()
        self.df[self.boolean] = self.df[self.boolean].astype('bool')
        self.df[self.string] = self.df[self.string].astype('str')
        self.df[self.numerical] = self.df[self.numerical].astype('float64')
        for featurename in self.datetime:
            self.df[featurename] = pd.to_datetime(self.df[featurename], errors='coerce')
        self.df.sort_values(by=self.shandler.get_value(['features', 'sentdate']), inplace=True)
        self.df.reset_index(inplace=True, drop=True)
        self.decisionfeatures()
        return self.df
    

    def decisionfeatures(self) -> None:
        """
        Add a 'decisionfeatures' columns to the DataFrame.
        """
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','anomalynumber']),0)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','issues']),None)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','anomalymeta']),None)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','spikenumber']),0)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','spikeissues']),None)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','spikemeta']),None)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','alertnumber']),0)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','alert']),None)
        self.df.insert(len(self.df.columns),self.shandler.get_value(['features','alertmeta']),None)        