import traceback
import numpy as np
import pandas as pd
from src.common.s3aws import S3FileHandler
from src.util.accessconfig import (Staticconfig )
from src.db.databaseconnection import MYSQLEngine
from src.preprocess.datapreprocess import Preprocess
from datetime import datetime ,timedelta
import requests
import json

class Detect:
    
    
    def __init__(self, Dynamicconfig: dict, data: pd.DataFrame ,database: pd.DataFrame ,mssqlquery ,log):
        self.shandler = Staticconfig()
        self.dynamicconfig = Dynamicconfig
        self.database = database.copy()
        self.mssqlquery = mssqlquery 
        self.alarmdata = None
        self.log = log
        self.data = data
        self.rtuname = str(self.dynamicconfig['rtuname'])
        self.glob_tally = {}
        self.glob_threshold = {}
        self.glob_spike = {}
        self.tally = {
                      'lowPhaseVoltage2':{},
                      'highPhaseVoltalarm2':{},
                      'lowPhaseVoltage1':{},
                      'highPhaseVoltalarm1':{},
                      'rmfail':{},
                      'dcem':{},
                      'lmu':{},
                      'ctr':{},
                      'smpsoverload':{},
                      'battovercharge': {},
                      'battovercharge_i':{}
                      }
        self.threshhold = {
                      'lowPhaseVoltage2':{},
                      'highPhaseVoltalarm2':{},
                      'lowPhaseVoltage1':{},
                      'highPhaseVoltalarm1':{},
                      'rmfail':{},
                      'dcem':{},
                      'lmu':{},
                      'ctr':{},
                      'smpsoverload':{},
                      'battovercharge': {},
                      'battovercharge_i':{}
                      }
        


    def alarmraised(self,Alarmcode:str,sentdate, threshold:list = []) -> None:
        body = None
        try:
            date_string = sentdate.strftime(r'%Y-%m-%d %H:%M:%S')
            body = {"Site_Id":self.dynamicconfig['siteid'],"RTUType":self.dynamicconfig['rtu'],"FeedAlarmStatus":f"{Alarmcode}-O","Receiveddate":date_string , "Threshold" :json.dumps(threshold)}
            self.log.info(f"{body}")
            self.mssqlquery.spalarm(body)
        except Exception as error:
            self.log.error(f"{body},{error}")
            pass

        
    def alarmclose(self,Alarmcode:str,sentdate,threshold:list = []) -> None:
        body =None
        try:
            date_string = sentdate.strftime(r'%Y-%m-%d %H:%M:%S')
            body = {"Site_Id":self.dynamicconfig['siteid'],"RTUType":self.dynamicconfig['rtu'],"FeedAlarmStatus":f"{Alarmcode}-C","Receiveddate":date_string, "Threshold" :json.dumps(threshold)}
            self.log.info(f"{body}")
            self.mssqlquery.spalarm(body)
        except Exception as error:
            self.log.error(f"{body},{error}")
            pass



    def process_results(self,input_list,check,threshold):
        siteid = str(self.dynamicconfig['siteid'])
        limiter_time = pd.to_datetime(self.dynamicconfig['currenttime']) - timedelta(minutes=11)
        result_list = []
        sublist = []
        try:
            self.database["siteid"] = self.database["siteid"].astype(str)
        except:
            pass
        columns_to_convert = self.shandler.get_value(['alarm'])
        for column in columns_to_convert:
            try:
                if  column != 'BHCR':
                    self.database[column] = pd.to_datetime(self.database[column], errors='coerce')
            except:
                pass
        # check if the alarm is already existing in both database  mssql and mysql .If there is mismatch we will close the alarm
        if (check in self.dynamicconfig['alarms']) and (self.database.loc[self.database["siteid"] == siteid, check].isnull().all()) and check != 'SMPSO':
            end = pd.Timestamp(self.data['SentDate'][self.data['SentDate'] >= self.filter_time].head(1).values[0])
            # self.database.loc[self.database['siteid'] == siteid,check] = end
            self.alarmclose(Alarmcode=check,sentdate= end)
        elif (check not in self.dynamicconfig['alarms']) and (not self.database.loc[self.database["siteid"] == siteid, check].isnull().all()) and check != 'SMPSO':
            # end = pd.Timestamp(self.database.loc[self.database["siteid"] == siteid, check].values[0])
            # self.alarmraised(Alarmcode=check,sentdate= end)
            self.database.loc[self.database['siteid'] == siteid,check] = pd.NaT

        # find if siteid exist in the database, we will check if input's first is the border or not if not we will close the ticket.
        if siteid in list(self.database[self.database[check].notnull()]["siteid"]) and check != 'SMPSO' :
            end = pd.Timestamp(self.data['SentDate'][self.data['SentDate'] >= self.filter_time].head(1).values[0])
            if  len(input_list) > 0 and end != self.data.loc[input_list[0], 'SentDate'] :
                self.database.loc[self.database['siteid'] == siteid,check] = pd.NaT
                self.alarmclose(Alarmcode=check,sentdate= end)
            elif len(input_list) == 0:
                self.database.loc[self.database['siteid'] == siteid,check] = pd.NaT
                self.alarmclose(Alarmcode=check,sentdate= end)

        # Identify continuous data points and group them
        for i in range(len(input_list)):
            if i == 0 or input_list[i] != input_list[i-1] + 1:
                if len(sublist) > 0:
                    result_list.append(sublist)
                sublist = [input_list[i]]
            else:
                sublist.append(input_list[i])
        if len(sublist) > 0:
            result_list.append(sublist)
        
        try: 
            if len(result_list) != 0:
                for result in result_list:
                    
                    start = self.data.loc[result[0], 'SentDate']
                    end = self.data.loc[result[-1], 'SentDate']
                    open_threshold = threshold[result[0]]
                    close_threshold = threshold[result[-1]]
                    try:
                        self.database["siteid"] = self.database["siteid"].as_type(str)
                    except:
                        pass
                    # if bucket last index is not the last index of main data (but indexs are found)
                    if result[-1] != len(self.data) - 1:
                        # Check if site exist in the database nrtticket
                        if siteid in self.database["siteid"].values:
                            compare = self.database.loc[self.database['siteid'] == siteid, check]
                            # check the preticular alarm is updated if yes
                            if not compare.empty :
                                first_value = compare.iloc[0]  # Get the first value
                                # Compare with start and end dates
                                if first_value is not None and (first_value <= pd.to_datetime(start) or first_value <= pd.to_datetime(end))  and check != 'SMPSO' :
                                # check is the already existing date in smaller than start or end date then close the alarm
                                    self.database.loc[self.database['siteid'] == siteid,check] = pd.NaT
                                    self.alarmclose(Alarmcode=check,sentdate= end,threshold=close_threshold)
                                    
                            else:
                                # if site exist due to other alarms , and the evaluating alarm is not in database raise and close the ticket
                                self.alarmraised(Alarmcode=check,sentdate= start,threshold=open_threshold)
                                if check != 'SMPSO':
                                    self.alarmclose(Alarmcode=check,sentdate= end,threshold=close_threshold)
                                
                        else:
                            # if the site do not exist in the database raise and close the ticket
                            self.alarmraised(Alarmcode=check,sentdate= start,threshold=open_threshold)
                            if check != 'SMPSO':
                                self.alarmclose(Alarmcode=check,sentdate= end,threshold=close_threshold)
                            
                    # if bucket last index is the last index of main data (indexs are found)
                    elif result[-1] == len(self.data) - 1 and end >= limiter_time:
                        # Check if site exist in the database nrtticket
                        if siteid in self.database["siteid"].values:
                            compare = self.database.loc[self.database['siteid'] == siteid, check]
                            # check the preticular alarm is updated if yes let it set as it is
                            if not compare.empty :
                                pass
                            else:
                                # else raise if there is no entry
                                self.database.loc[self.database['siteid'] == siteid,check] = start
                                self.alarmraised(Alarmcode=check,sentdate= start,threshold=open_threshold)
                                
                        else:
                            # Check if site do not exist in the database nrtticket add ticket
                            new_data = pd.DataFrame({
                                "siteid": [siteid], 
                                check : [start]
                            })
                            new_data["siteid"] = new_data["siteid"].astype(str)
                            new_data[check] = pd.to_datetime(new_data[check])
                            self.database = pd.concat([self.database, new_data], axis=0, ignore_index=True)
                            self.alarmraised(Alarmcode=check,sentdate= start,threshold=open_threshold)
                        

                    elif result[-1] == len(self.data) - 1 and end < limiter_time:
                        # Check if site exist in the database nrtticket
                        if siteid in self.database["siteid"].values:
                            compare = self.database.loc[self.database['siteid'] == siteid, check]
                            # check the preticular alarm is updated if yes let it set as it is
                            if not compare.empty  and check != 'SMPSO':
                                self.database.loc[self.database['siteid'] == siteid,check] = pd.NaT
                                self.alarmclose(Alarmcode=check,sentdate= end,threshold=close_threshold)
                                
                            else:
                                # else raise if there is no entry
                                self.alarmraised(Alarmcode=check,sentdate= start,threshold=open_threshold)
                                if check != 'SMPSO':
                                    self.alarmclose(Alarmcode=check,sentdate= end,threshold=close_threshold)
                                
                        else:
                            # Check if site do not exist in the database nrtticket add ticket
                            self.alarmraised(Alarmcode=check,sentdate= start,threshold=open_threshold)
                            if check != 'SMPSO':
                                self.alarmclose(Alarmcode=check,sentdate= end,threshold=close_threshold)
                            
            else:
                if siteid in list(self.database[self.database[check].notnull()]["siteid"])  and check != 'SMPSO':
                    end = pd.Timestamp(self.data['SentDate'][self.data['SentDate'] >= self.filter_time].head(1).values[0])
                    self.database.loc[self.database['siteid'] == siteid,check] = pd.NaT
                    self.alarmclose(Alarmcode=check,sentdate= end,threshold=close_threshold)
        except Exception as error:
            self.log.error(f"{siteid},{error}")
        del input_list,result_list,sublist,columns_to_convert

    def overcharge_results(self,input,check):
        siteid = str(self.dynamicconfig['siteid'])
        limiter_time = pd.to_datetime(self.dynamicconfig['currenttime'])
        try:
            self.database["siteid"] = self.database["siteid"].astype(str)
            self.database["BHCR"] = self.database["BHCR"].astype(str)
        except:
            pass
        # columns_to_convert = self.shandler.get_value(['alarm'])
        # for column in columns_to_convert:
        #     try:
        #         self.database[column] = pd.to_datetime(self.database[column], errors='coerce')
        #     except:
        #         pass
        # check if the alarm is already existing in both database  mssql and mysql .If there is mismatch we will close the alarm
        if (check in self.dynamicconfig['alarms']) and (self.database.loc[self.database["siteid"] == siteid, check].isnull().all()):
            end = pd.Timestamp(self.data['SentDate'][self.data['SentDate'] >= self.filter_time].head(1).values[0])
            self.alarmclose(Alarmcode=check,sentdate= end)
        elif (check not in self.dynamicconfig['alarms']) and (not self.database.loc[self.database["siteid"] == siteid, check].isnull().all()):
            # end = pd.Timestamp(self.database.loc[self.database["siteid"] == siteid, check].values[0])
            self.database.loc[self.database['siteid'] == siteid, check] = np.nan

        
        for status in input.values():
            if siteid in self.database["siteid"].values:
                # Retrieve the current value of the alarm for the site
                compare = self.database.loc[self.database['siteid'] == siteid, check]

                # Case 1: If alarm is not set and status is True, set it to 'O' and raise alarm
                if compare.empty and status[0]:
                    self.database.loc[self.database['siteid'] == siteid, check] = 'O'
                    self.alarmraised(Alarmcode=check, sentdate=limiter_time ,threshold=status[1])

                # Case 2: If alarm is 'O' and status is False, set it to 'P'
                elif not compare.empty and compare.iloc[0] == 'O' and not status[0]:
                    self.database.loc[self.database['siteid'] == siteid, check] = 'P'

                # Case 3: If alarm is 'P' and status is False, close the alarm and set it to NaN
                elif not compare.empty and compare.iloc[0] == 'P' and not status[0]:
                    self.database.loc[self.database['siteid'] == siteid, check] = np.nan
                    self.alarmclose(Alarmcode=check, sentdate=limiter_time,threshold=status[1])

                # Case 4: If alarm is 'P' and status is True, set it back to 'O'
                elif not compare.empty and compare.iloc[0] == 'P' and status[0]:
                    self.database.loc[self.database['siteid'] == siteid, check] = 'O'

                # No action needed for other cases
                else:
                    pass
            elif siteid not in self.database["siteid"].values and status[0] == True:
            # Check if site do not exist in the database nrtticket add ticket
                new_data = pd.DataFrame({
                    "siteid": [siteid], 
                    check : ['O']
                })
                new_data["siteid"] = new_data["siteid"].astype(str)
                new_data[check] = new_data[check]
                self.database = pd.concat([self.database, new_data], axis=0, ignore_index=True)
                self.alarmraised(Alarmcode=check,sentdate= limiter_time,threshold=status[1])
  
        
                      

    def main_condition(self) -> pd.DataFrame:
        anomal_check  = self.glob_tally.copy()
        # spike_check = self.glob_spike.copy()
        glob_check = [anomal_check, 'AnomalyNumber', 'Issues', 'AnomalyMeta']
        # update the filter time as we might find the skipped data packets.
        self.filter_time = pd.to_datetime(self.dynamicconfig['runtime']) + timedelta(minutes=60)
        # for glob_check in glob_data:
        indices = np.where(self.data['SentDate'] >= self.filter_time)[0]
        if indices.size != 0:
            min_index = indices.min()
            value = sorted(list(set(list(anomal_check["lowPhaseVoltage1"].keys()) + list(anomal_check["lowPhaseVoltage2"].keys()))))
            input_list = [x for x in value if x >= min_index]
            eblv = self.glob_threshold['lowPhaseVoltage1'].copy()  
            eblv.update(self.glob_threshold['lowPhaseVoltage2'])
            self.process_results(input_list=input_list,check='EB_LV',threshold = eblv)

            value = sorted(list(set(list(anomal_check["highPhaseVoltalarm2"].keys()) + list(anomal_check["highPhaseVoltalarm1"].keys()))))
            input_list = [x for x in value if x >= min_index]
            ebhv = self.glob_threshold['highPhaseVoltalarm1'].copy()  
            ebhv.update(self.glob_threshold['highPhaseVoltalarm2'])
            self.process_results(input_list=input_list,check='EB_HV',threshold = ebhv)
            
            value = anomal_check["battovercharge"]
            self.overcharge_results(input=value,check='BHCR')

            if self.dynamicconfig['rtu'] != 2081:
                value = sorted(list(anomal_check["dcem"].keys()))
                input_list = [x for x in value if x >= min_index]
                dcm = self.glob_threshold['dcem'].copy()
                self.process_results(input_list=input_list,check='DCM',threshold = dcm)

            if self.dynamicconfig['rtu'] == 2370:

                value = sorted(list(anomal_check["smpsoverload"].keys()))
                input_list = [x for x in value if x >= min_index]
                smpso = self.glob_threshold['smpsoverload'].copy()
                self.process_results(input_list=input_list,check='SMPSO',threshold = smpso)

                value = sorted(list(anomal_check["rmfail"].keys()))
                input_list = [x for x in value if x >= min_index]
                rmfail = self.glob_threshold['rmfail'].copy()
                self.process_results(input_list=input_list,check='RMFAIL',threshold = rmfail)

                # value = sorted(list(anomal_check["lmu"].keys()))
                # input_list = [x for x in value if x >= min_index]
                # lmu = self.glob_threshold['lmu'].copy()
                # self.process_results(input_list=input_list,check='LMUF',threshold = lmu)

                value = sorted(list(anomal_check["ctr"].keys()))
                input_list = [x for x in value if x >= min_index]
                lmu = self.glob_threshold['ctr'].copy()
                self.process_results(input_list=input_list,check='CTRFail',threshold = lmu)

            
            #value = sorted(list(anomal_check["csu"].keys()))
            #input_list = [x for x in value if x >= min_index]
            #self.process_results(input_list=input_list,check='CSUF')

            
            del input_list
            # Please update the delete query for the alarm code
    
            for x in indices:
                issue = []
                meta = []

                if x in glob_check[0]['dcem'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['dcem'][x].split(',')[0])
                    meta.append(glob_check[0]['dcem'][x].split(',')[1])
                else:
                    pass
                
                if x in glob_check[0]['smpsoverload'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['smpsoverload'][x].split(',')[0])
                    meta.append(glob_check[0]['smpsoverload'][x].split(',')[1])
                else:
                    pass
                
                if x in glob_check[0]['rmfail'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['rmfail'][x].split(',')[0])
                    meta.append(glob_check[0]['rmfail'][x].split(',')[1])
                else:
                    pass
                
                if x in glob_check[0]['lowPhaseVoltage2'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['lowPhaseVoltage2'][x].split(',')[0])
                    meta.append(glob_check[0]['lowPhaseVoltage2'][x].split(',')[1])
                    
                else: 
                    pass

                if x in glob_check[0]['highPhaseVoltalarm2'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['highPhaseVoltalarm2'][x].split(',')[0])
                    meta.append(glob_check[0]['highPhaseVoltalarm2'][x].split(',')[1])
                    
                else: 
                    pass
                

                if x in glob_check[0]['battovercharge_i'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['battovercharge_i'][x].split(',')[0])
                    meta.append(glob_check[0]['battovercharge_i'][x].split(',')[1])

                else: 
                    pass

                if x in glob_check[0]['lowPhaseVoltage1'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['lowPhaseVoltage1'][x].split(',')[0])
                    meta.append(glob_check[0]['lowPhaseVoltage1'][x].split(',')[1])

                else: 
                    pass

                if x in glob_check[0]['highPhaseVoltalarm1'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['highPhaseVoltalarm1'][x].split(',')[0])
                    meta.append(glob_check[0]['highPhaseVoltalarm1'][x].split(',')[1])
                else: 
                    pass

                if x in glob_check[0]['lmu'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['lmu'][x].split(',')[0])
                    meta.append(glob_check[0]['lmu'][x].split(',')[1])
                else:
                    pass

                if x in glob_check[0]['ctr'].keys():
                    self.data.loc[x, glob_check[1]] += 1
                    issue.append(glob_check[0]['ctr'][x].split(',')[0])
                    meta.append(glob_check[0]['ctr'][x].split(',')[1])
                else:
                    pass


                if len(issue) > 0:
                    self.data.loc[x, glob_check[2]] =  " / ".join(issue)
                    self.data.loc[x, glob_check[3]] =  " / ".join(meta)
                else:
                    self.data.loc[x, glob_check[2]] = "No" 
                    self.data.loc[x, glob_check[3]] = "No" 
                del issue , meta 

        filtered_df = self.database[self.database['siteid'] == str(self.dynamicconfig['siteid'])]
        self.alarmdata = filtered_df
        del filtered_df,anomal_check,glob_check
       

    def highvoltalarmcondition(self) -> None:
        space = {}
        temp = {}
        flag_temp = 0
        flag = 0
        upper = float(self.shandler.get_value(['default', 'highvoltalarmvolt']))
        lower = upper - float(self.shandler.get_value(['limit', 'highvoltalarmlimit']))
        
        start = None

        def timediff(x):
            test = x - start
            return test.total_seconds()
        
        for x in range(len(self.data)):
            try:
                if self.data.loc[x, self.shandler.get_value(['features', 'battv'])] >= upper and flag_temp == 0 and flag == 0:
                    flag_temp = 1
                    temp[x] = f"Faulty HighVoltAlarm,F5"
                elif self.data.loc[x, self.shandler.get_value(['features', 'battv'])] >= upper and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 900 and flag_temp == 1 and flag == 0 :
                    temp[x] = f"Faulty HighVoltAlarm,F5"
                elif self.data.loc[x, self.shandler.get_value(['features', 'battv'])] >= upper and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 900 and flag_temp == 1 and flag == 0:
                    flag = 1 
                    flag_temp = 0
                    space.update(temp)
                    space[x] = f"Faulty HighVoltAlarm,F5"
                    temp = {}
                elif self.data.loc[x, self.shandler.get_value(['features', 'battv'])] > lower and flag == 1:
                    space[x] = f"Faulty HighVoltAlarm,F5"
                elif self.data.loc[x, self.shandler.get_value(['features', 'battv'])] <= lower :
                    flag_temp = 0
                    flag = 0
                    temp={}
                else:
                    pass
            except Exception as e :
                # print(e,'highvoltalarmcondition')
                pass
        self.glob_tally['HighVoltAlarm'], self.glob_spike['HighVoltAlarm'] = self.continuous_data(space.copy())
        del space

    def dcemsensor(self) -> None:
        temp_threshold = {}
        threshold = {} 
        space = {}
        temp = {}
        flag_temp = 0
        flag = 0

        def timediff_open(x):
            test = x - start
            return test.total_seconds()
        
        try : 
            customerid = self.dynamicconfig['customerid'][0]
        except Exception as e :
            customerid = 0 
        
            
            
        if self.dynamicconfig['rtu'] == 2158 :  
            for x in range(len(self.data)):
                try:
                    channel = self.data.loc[x, self.shandler.get_value(['features', 'chonecurent'])]+self.data.loc[x, self.shandler.get_value(['features', 'chtwocurent'])]+self.data.loc[x, self.shandler.get_value(['features', 'chthreecurent'])]+self.data.loc[x, self.shandler.get_value(['features', 'chfourcurent'])]
                    if ((self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] > 5 and self.data.loc[x,self.shandler.get_value(['features', 'battv'])] > 48)  or self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == True ) and flag_temp == 0 and flag == 0:
                        flag_temp = 1
                        start = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
                        temp[x] = "DCEM Failed,S1"
                        temp_threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                    elif ((self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] > 5 and self.data.loc[x,self.shandler.get_value(['features', 'battv'])] > 48  ) or self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == True ) and  timediff_open(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 600 and flag_temp == 1 and flag == 0:
                        temp[x] = "DCEM Failed,S1"
                        temp_threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                    elif ((self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] > 5 and self.data.loc[x,self.shandler.get_value(['features', 'battv'])] > 48)  or self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == True) and timediff_open(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 600 and flag_temp == 1 and flag == 0:
                        flag_temp = 0
                        flag = 1
                        space.update(temp) 
                        space[x] = "DCEM Failed,S1"
                        threshold.update(temp_threshold)
                        threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                        temp= {}
                        temp_threshold = {}
                    elif (self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel > 0 ) or (self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] <= 1 ) :
                        flag_temp = 0
                        flag = 0
                        temp = {}
                        start = None
                        temp_threshold = {}# threshold
                    elif flag == 1:
                        space[x] = "DCEM Failed,S1"
                        threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                    else:
                        pass

                except Exception as error:
                    print(error)
                    pass



        elif self.dynamicconfig['rtu'] == 2370 and customerid != 69 :  
            for x in range(len(self.data)):
                try:
                    if self.data.loc[x, self.shandler.get_value(['features', 'deviceid'])][:5] == '91044': 
                        channel = self.data.loc[x, self.shandler.get_value(['features', 'chonecurent'])]+self.data.loc[x, self.shandler.get_value(['features', 'chtwocurent'])]+self.data.loc[x, self.shandler.get_value(['features', 'chthreecurent'])]+self.data.loc[x, self.shandler.get_value(['features', 'chfourcurent'])]
                        if ((self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] > 5 and self.data.loc[x,self.shandler.get_value(['features', 'battv'])] > 48)  or self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == True ) and flag_temp == 0 and flag == 0:
                            flag_temp = 1
                            start = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
                            temp[x] = "DCEM Failed,S1"
                            temp_threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]# threshold
                        elif ((self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] > 5 and self.data.loc[x,self.shandler.get_value(['features', 'battv'])] > 48  ) or self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == True ) and  timediff_open(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 600 and flag_temp == 1 and flag == 0:
                            temp[x] = "DCEM Failed,S1"
                            temp_threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                        elif ((self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] > 5 and self.data.loc[x,self.shandler.get_value(['features', 'battv'])] > 48)  or self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == True) and timediff_open(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 600 and flag_temp == 1 and flag == 0:
                            flag_temp = 0
                            flag = 1
                            space.update(temp) 
                            space[x] = "DCEM Failed,S1"
                            threshold.update(temp_threshold)
                            threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                            temp= {}
                            temp_threshold = {}
                        elif (self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel > 0 ) or (self.data.loc[x, self.shandler.get_value(['features', 'dcmcomfailalarm'])] == False and channel == 0 and self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])] <= 1 ) :
                            flag_temp = 0
                            flag = 0
                            temp = {}
                            start = None
                            temp_threshold = {}
                        elif flag == 1:
                            space[x] = "DCEM Failed,S1"
                            threshold[x] = [{"Alarm":"DCM","AlarmLog":[{"Name":"Load Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Channel Current","Value":channel},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                        else:
                            pass

                except Exception as error:
                    print(error)
                    pass
        self.glob_tally['dcem'], self.glob_spike['dcem'] = self.continuous_data(space.copy())
        self.glob_threshold['dcem'],spike = self.continuous_data(threshold.copy())
        del space,temp, threshold,temp_threshold ,spike

    def rmfail(self) -> None:
        temp_threshold = {}
        threshold = {}
        start = None
        stop = None
        temp = {}
        space = {}
        flag = 0
        flag_temp = 0
        close = 0
        
        def timediff_open(x):
            test = x - start
            return test.total_seconds()
        
        def timediff_close(x):
            test = x - stop
            return test.total_seconds()
        
        if len(self.glob_tally['smpsoverload']) == 0 or "SMPSO" not in self.dynamicconfig['alarms']:
        
            try : 
                customerid = self.dynamicconfig['customerid'][0]
            except Exception as e :
                customerid = 0 

            if customerid == 69:
                for x in range(len(self.data)):
                    try:
                        if (175 <= self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] <= 300) and self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3 and self.data.loc[x, self.shandler.get_value(['features', 'battv'])] <= 51 and flag == 0 and flag_temp == 0:
                            flag_temp = 1
                            start = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
                            temp[x] = "Rectifier Fail Alarm,R4"
                            temp_threshold[x] = [{"Alarm":"RMFail","AlarmLog":[{"Name":"EB Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Discharge Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]# threshold
                        elif (175 <= self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] <= 300) and self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3 and self.data.loc[x, self.shandler.get_value(['features', 'battv'])] <= 51  and flag_temp == 1 and flag == 0 and timediff_open(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 600 :
                            temp[x] = "Rectifier Fail Alarm,R4"
                            temp_threshold[x] = [{"Alarm":"RMFail","AlarmLog":[{"Name":"EB Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Discharge Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]# threshold
                        elif (175 <= self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] <= 300) and self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3 and self.data.loc[x, self.shandler.get_value(['features', 'battv'])] <= 51 and flag_temp == 1 and flag == 0 and timediff_open(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 600 :
                            flag_temp = 0
                            flag = 1
                            space.update(temp) 
                            space[x] = "Rectifier Fail Alarm,R4"
                            threshold.update(temp_threshold)
                            threshold[x] = [{"Alarm":"RMFail","AlarmLog":[{"Name":"EB Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Discharge Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]# threshold
                            temp= {}
                            temp_threshold = {}
                        elif (175 <= self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] <= 300) and self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3 and self.data.loc[x, self.shandler.get_value(['features', 'battv'])] <= 51  and flag_temp == 0 and flag == 1 and timediff_open(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 600 :
                            close = 0
                            stop = None
                            flag = 1
                            space[x] = "Rectifier Fail Alarm,R4"
                            threshold[x] = [{"Alarm":"RMFail","AlarmLog":[{"Name":"EB Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Discharge Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                        elif ((self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] <= 3) or  (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] < 175 ) or (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] > 300) or (self.data.loc[x, self.shandler.get_value(['features', 'battv'])] > 51) ) and flag == 1 and close == 0 :
                            space[x] = "Rectifier Fail Alarm,R4"
                            threshold[x] = [{"Alarm":"RMFail","AlarmLog":[{"Name":"EB Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Discharge Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                            close = 1
                            stop = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
                        elif ((self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] <= 3) or  (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] < 175 ) or (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] > 300) or (self.data.loc[x, self.shandler.get_value(['features', 'battv'])] > 51)) and flag == 1 and close == 1 and timediff_close(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 600  :
                            space[x] = "Rectifier Fail Alarm,R4"
                            threshold[x] = [{"Alarm":"RMFail","AlarmLog":[{"Name":"EB Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Discharge Current","Value":self.data.loc[x,self.shandler.get_value(['features', 'ebvoltage'])]},{"Name":"Battery Voltage","Value":self.data.loc[x,self.shandler.get_value(['features', 'battv'])]}]}]
                        elif (((self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] <= 3) or  (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] < 175 ) or (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] > 300) or (self.data.loc[x, self.shandler.get_value(['features', 'battv'])] > 51) ) and flag == 1 and timediff_close(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 600) or ( ((self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] <= 3) or  (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] < 175 ) or (self.data.loc[x, self.shandler.get_value(['features', 'ebvoltage'])] > 300) or (self.data.loc[x, self.shandler.get_value(['features', 'battv'])] > 51)) and flag == 0 and flag_temp == 1 ) :
                            flag = 0 
                            flag_temp = 0
                            close = 0
                            temp = {}
                            temp_threshold = {}
                        else:
                            pass
                    except Exception as error:
                        pass

        self.glob_tally['rmfail'], self.glob_spike['rmfail'] = self.continuous_data(space.copy())
        self.glob_threshold['rmfail'],spike = self.continuous_data(threshold.copy())
        del space,temp,start,stop , threshold, temp_threshold ,spike

    def highphasevoltage2(self) -> None:
        """
        Check EBAvail condition based on phase voltage.
        High Voltage Alarm >275 if persists for 5 Mins   
        Updates the 'space' dictionary with indices indicating faulty EBAvail conditions.

        Returns:
        None
        """
        if self.dynamicconfig['siteclassification'] == 1:
            limit = float(275)
        else:
            limit = float(245)
        threshold = {}
        space = {}
 
        for x in range(len(self.data)):

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])] > 0:
                rphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])])/2
            else:
                rphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])]

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])] > 0:
                bphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])])/2
            else:
                bphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])]
            
            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])] > 0:
                yphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])])/2
            else:
                yphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])]

            try:
                if (rphase > limit) or ( yphase > limit) or ( bphase > limit):
                    space[x] = f"High Voltage Alarm (greater than {limit} for more than 5mins),A3"
                    threshold[x] = [{"Alarm":"EB_HV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"5min"}]}]# threshold
                else:
                    pass
                
            except Exception as e :
                # print(e)
                pass
        self.glob_tally['highPhaseVoltalarm2'], self.glob_spike['highPhaseVoltalarm2'] = self.continuous_data(space.copy())
        self.glob_threshold['highPhaseVoltalarm2'],spike = self.continuous_data(threshold.copy())
        del space , threshold ,spike

    def lowphasevoltage2(self) -> None:
        """
        Check EBAvail condition based on phase voltage.
        Low Voltage Alarm <100V if persists for 5 Mins  
        Updates the 'space' dictionary with indices indicating faulty EBAvail conditions.

        Returns:
        None
        """
        if self.dynamicconfig['siteclassification'] == 1:
            limit = float(100)
        else: 
            limit = float(200)
        base = float(0)
        space = {}
        threshold = {}
        for x in range(len(self.data)):

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])] > 0:
                rphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])])/2
            else:
                rphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])]

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])] > 0:
                bphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])])/2
            else:
                bphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])]
            
            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])] > 0:
                yphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])])/2
            else:
                yphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])]


            try:
               
                if (base < rphase < limit) or (base< yphase < limit) or ( base < bphase < limit):
                    space[x] = f"Low Voltage Alarm (less than {limit} for more than 5mins),A4"
                    threshold[x] = [{"Alarm":"EB_LV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"5min"}]}]# threshold
                else:
                    pass
            
            except Exception as e :
                # print(e)
                pass
        self.glob_tally['lowPhaseVoltage2'], self.glob_spike['lowPhaseVoltage2'] = self.continuous_data(space.copy())
        self.glob_threshold['lowPhaseVoltage2'],spike = self.continuous_data(threshold.copy())
        del space, threshold  , spike


    def highphasevoltage1(self) -> None:
        """
        Check EBAvail condition based on phase voltage.
        High Voltage Alarm >255 if persists for one Hrs
        Updates the 'space' dictionary with indices indicating faulty EBAvail conditions.

        Returns:
        None
        """
        try : 
            customerid = self.dynamicconfig['customerid'][0]
        except Exception as e :
            customerid = 0 

        if customerid == 69:
            limit = float(260)
        else:
            limit = float(255)
        
        start = None
        space = {}
        temp = {}
        temp_threshold = {}
        threshold = {}
        flag = 0

        def timediff(x):
            test = x - start
            return test.total_seconds()

        for x in range(len(self.data)):

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])] > 0:
                rphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])])/2
            else:
                rphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])]

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])] > 0:
                bphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])])/2
            else:
                bphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])]
            
            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])] > 0:
                yphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])])/2
            else:
                yphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])]


            try:
                
                if ((rphase > limit) or ( yphase > limit) or ( bphase > limit)) and flag == 0:
                    flag = 1
                    start = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
                    temp[x] = f"High Voltage Alarm (greater than {limit} for more than 1hr),A5"
                    temp_threshold[x] = [{"Alarm":"EB_HV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"1hr"}]}]
                elif ((rphase > limit) or ( yphase > limit) or ( bphase > limit)) and flag == 1 and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 3600:
                    temp[x] = f"High Voltage Alarm (greater than {limit} for more than 1hr),A5"
                    temp_threshold[x] = [{"Alarm":"EB_HV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"1hr"}]}]
                elif ((rphase > limit) or ( yphase > limit) or ( bphase > limit)) and flag == 1 and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 3600:
                    space.update(temp) 
                    space[x] = f"High Voltage Alarm (greater than {limit} for more than 1hr),A5"
                    threshold.update(temp_threshold)
                    threshold[x] = [{"Alarm":"EB_HV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"1hr"}]}]
                    temp= {}
                    temp_threshold = {}
                elif ((rphase <= limit) and ( yphase <= limit) and ( bphase <= limit)) and flag == 1:
                    flag = 0
                    temp = {}
                    temp_threshold = {}
                else:
                    pass
            
            except Exception as e :
                # print(e)
                pass
        self.glob_tally['highPhaseVoltalarm1'] = space.copy()
        self.glob_threshold['highPhaseVoltalarm1'] = threshold.copy()
        del space,temp, threshold , temp_threshold

    def ctrfaulty(self) :
        """
        Check for faulty control status.
        Faulty Control Alarm if CE_DELTA_PP, CE_VNT24KW_PP, CE_VNT7KW_PP, TVI_DELTA_PP are all 0 or all 1.
        Updates the 'pace' dictionary with indices indicating faulty control conditions."""

        threshold = {}
        pace = {}
        space = {}
        temp = {}
        flag_temp = 0
        delta = {}
        flag = 0
        close = 0
        start = None
        stop = None

        def timediff(x):
            test = x - start
            return test.total_seconds()
        
        def timediff_close(x):
            test = x - stop
            return test.total_seconds()
        
        try : 
            customerid = self.dynamicconfig['customerid'][0]
        except Exception as e :
            customerid = 0 
        # Dynamically fetch column names using self.shandler.get_value
        check = self.data[
            [
                self.shandler.get_value(['features', "ce_delta_pp"]),
                self.shandler.get_value(['features', "ce_vnt24kw_pp"]),
                self.shandler.get_value(['features', "ce_vnt7kw_pp"]),
                self.shandler.get_value(['features', "tvi_delta_pp"])
            ]
        ].any().any()

        if   customerid == 69 and 'PPCF' not in self.dynamicconfig['alarms'] and check:
            
            data = self.mssqlquery.lmudata(Dynamicconfig = self.dynamicconfig) 
            if not data.empty:
                
                data = data.reset_index(drop = True)
                try: 
                  
                    data[[self.shandler.get_value(['features', 'loadcurrent']) ,  self.shandler.get_value(['features', 'battv'])]] = data[[self.shandler.get_value(['features', 'loadcurrent']) , self.shandler.get_value(['features', 'battv'])]].astype(float)
                    data[self.shandler.get_value(['features', 'sentdate'])] = pd.to_datetime(data[self.shandler.get_value(['features', 'sentdate'])])
                    
                except Exception as e:
                    pass

                for x in range(len(data)):
                    try:  
                        if data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] < 3 and data.loc[x, self.shandler.get_value(['features', 'battv'])] >= 48  and flag_temp == 0 and flag == 0:
                            flag_temp = 1
                            start = data.loc[x,self.shandler.get_value(['features', 'sentdate'])]
                            temp[x] = "CTR Fault detected,Z1"
                        elif flag_temp == 1 and flag == 0 and data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] < 3 and timediff(data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 86400 :
                            close = 0
                            stop = None
                            temp.update(delta)
                            temp[x] = "CTR Fault detected,Z1"
                            delta = {}
                        elif flag_temp == 1 and flag == 0 and data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] < 3 and timediff(data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 86400 :
                            flag_temp = 0 
                            flag = 1
                            close = 0
                            stop = None
                            temp.update(delta)
                            delta = {}
                            pace.update(temp)
                            pace[x] = "CTR Fault detected,Z1"
                            temp = {}
                            
                        elif (data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] >= 3 )  :
                            if close == 0:
                                close = 1
                                stop = data.loc[x,self.shandler.get_value(['features', 'sentdate'])]
                                delta[x] = "CTR Fault detected,Z1"
                            elif close == 1 and timediff_close(data.loc[x,self.shandler.get_value(['features', 'sentdate'])]) >= 1200:
                                # If close condition persists for 20 minutes
                                flag = 0
                                flag_temp = 0
                                close = 0
                                start = None
                                stop = None
                                temp = {}
                                delta = {}
                            else:
                                delta[x] = "CTR Fault detected,Z1"
                        elif flag == 1:
                            close = 0
                            stop = None
                            temp.update(delta)
                            delta = {}
                            pace.update(temp)
                            pace[x] = "CTR Fault detected,Z1"
                            temp = {}
                        else:
                            pass
                    except Exception as e :
                        self.log.error(f"{e,traceback.format_exc()}")
                del data
            if len(pace) > 0:
                for x in range(len(self.data)):
                    space[x] = "CTR Fault detected,Z1" 
                    threshold[x] = [{"Alarm":"CTRFail","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
                pace={}

        self.glob_tally['ctr'], self.glob_spike['ctr'] = self.continuous_data(space.copy())
        self.glob_threshold['ctr'] ,spike = self.continuous_data(threshold.copy())
        del space,temp ,threshold, spike 




    # def lmufaulty(self):
    #     temp_threshold = {}
    #     threshold = {}
    #     pace = {}
    #     space = {}
    #     temp = {}
    #     flag_temp = 0
    #     delta = {}
    #     flag = 0
    #     close = 0
    #     start = None
    #     stop = None

    #     def timediff(x):
    #         test = x - start
    #         return test.total_seconds()
        
    #     def timediff_close(x):
    #         test = x - stop
    #         return test.total_seconds()
        
    #     try : 
    #         customerid = self.dynamicconfig['customerid'][0]
    #     except Exception as e :
    #         customerid = 0 
    #     # Dynamically fetch column names using self.shandler.get_value
    #     check = self.data[
    #         [
    #             self.shandler.get_value(['features', "ce_delta_pp"]),
    #             self.shandler.get_value(['features', "ce_vnt24kw_pp"]),
    #             self.shandler.get_value(['features', "ce_vnt7kw_pp"]),
    #             self.shandler.get_value(['features', "tvi_delta_pp"])
    #         ]
    #     ].any().any()

    #     if customerid == 69 and 'LMUCF' not in self.dynamicconfig['alarms'] and (not self.dynamicconfig['makercheck'] or not check):
            
    #         data = self.mssqlquery.lmudata(Dynamicconfig = self.dynamicconfig) 
    #         if not data.empty:
                
    #             data = data.reset_index(drop = True)
    #             try: 
                  
    #                 data[[self.shandler.get_value(['features', 'loadcurrent']) ,  self.shandler.get_value(['features', 'battv'])]] = data[[self.shandler.get_value(['features', 'loadcurrent']) , self.shandler.get_value(['features', 'battv'])]].astype(float)
    #                 data[self.shandler.get_value(['features', 'sentdate'])] = pd.to_datetime(data[self.shandler.get_value(['features', 'sentdate'])])
                    
    #             except Exception as e:
    #                 pass

    #             for x in range(len(data)):
    #                 try:  
    #                     if data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] < 3 and data.loc[x, self.shandler.get_value(['features', 'battv'])] >= 48  and flag_temp == 0 and flag == 0:
    #                         flag_temp = 1
    #                         start = data.loc[x,self.shandler.get_value(['features', 'sentdate'])]
    #                         temp[x] = "LMU Fault detected,Z1"
    #                     elif flag_temp == 1 and flag == 0 and data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] < 3 and timediff(data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 86400 :
    #                         close = 0
    #                         stop = None
    #                         temp.update(delta)
    #                         temp[x] = "LMU Fault detected,Z1"
    #                         delta = {}
    #                     elif flag_temp == 1 and flag == 0 and data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] < 3 and timediff(data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 86400 :
    #                         flag_temp = 0 
    #                         flag = 1
    #                         close = 0
    #                         stop = None
    #                         temp.update(delta)
    #                         delta = {}
    #                         pace.update(temp)
    #                         pace[x] = "LMU Fault detected,Z1"
    #                         temp = {}
                            
    #                     elif (data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] >= 3 )  :
    #                         if close == 0:
    #                             close = 1
    #                             stop = data.loc[x,self.shandler.get_value(['features', 'sentdate'])]
    #                             delta[x] = "LMU Fault detected,Z1"
    #                         elif close == 1 and timediff_close(data.loc[x,self.shandler.get_value(['features', 'sentdate'])]) >= 1200:
    #                             # If close condition persists for 20 minutes
    #                             flag = 0
    #                             flag_temp = 0
    #                             close = 0
    #                             start = None
    #                             stop = None
    #                             temp = {}
    #                             delta = {}
    #                         else:
    #                             delta[x] = "LMU Fault detected,Z1"
    #                     elif flag == 1:
    #                         close = 0
    #                         stop = None
    #                         temp.update(delta)
    #                         delta = {}
    #                         pace.update(temp)
    #                         pace[x] = "LMU Fault detected,Z1"
    #                         temp = {}
    #                     else:
    #                         pass
    #                 except Exception as e :
    #                     self.log.error(f"{e,traceback.format_exc()}")
    #             del data
    #         if len(pace) > 0:
    #             for x in range(len(self.data)):
    #                 space[x] = "LMU Fault detected,Z1" 
    #                 threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #             pace={}

            
    #         start = None
    #         flag_temp = 0
    #         flag = 0
    #         temp = {}
    #         if self.dynamicconfig['batterytype'] == 2283:
    #             for x in range(len(self.data)):
    #                 try:
    #                     if self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0 and self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3 and self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == False and self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == False  and ((self.data.loc[x, self.shandler.get_value(['features', 'libonecomfailalarm'])] == False and self.data.loc[x, self.shandler.get_value(['features', 'libonecurrent'])] != 0 ) or (self.data.loc[x, self.shandler.get_value(['features', 'libtwocomfailalarm'])] == False and self.data.loc[x, self.shandler.get_value(['features', 'libtwocurrent'])] != 0 )) and flag_temp == 0 and flag == 0:
    #                         flag_temp = 1
    #                         temp[x] = "LMU Fault detected,Z1"
    #                         temp_threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #                         start = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
    #                     elif (self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0) and (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3) and (self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == False) and (self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == False ) and ((self.data.loc[x, self.shandler.get_value(['features', 'libonecomfailalarm'])] == False and self.data.loc[x, self.shandler.get_value(['features', 'libonecurrent'])] != 0 ) or (self.data.loc[x, self.shandler.get_value(['features', 'libtwocomfailalarm'])] == False and self.data.loc[x, self.shandler.get_value(['features', 'libtwocurrent'])] != 0 )) and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 600 and flag_temp == 1 and flag == 0:
    #                         temp[x] = "LMU Fault detected,Z1"
    #                         temp_threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #                     elif (self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0) and (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3) and (self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == False) and (self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == False)  and ((self.data.loc[x, self.shandler.get_value(['features', 'libonecomfailalarm'])] == False and self.data.loc[x, self.shandler.get_value(['features', 'libonecurrent'])] != 0 ) or (self.data.loc[x, self.shandler.get_value(['features', 'libtwocomfailalarm'])] == False and self.data.loc[x, self.shandler.get_value(['features', 'libtwocurrent'])] != 0 )) and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 600 and flag_temp == 1 and flag == 0:
    #                         flag_temp = 0 
    #                         flag = 1
    #                         space.update(temp) 
    #                         space[x] = "LMU Fault detected,Z1"
    #                         threshold.update(temp_threshold)
    #                         threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #                         temp= {}
    #                         temp_threshold = {}
    #                     elif (self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == True ) or ((self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0) and (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] <= 3 )) or (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] == 0 ) or (self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == True) : 
    #                         flag = 0
    #                         flag_temp = 0
    #                         temp = {}
    #                         temp_threshold = {}
    #                         start = None
    #                     elif flag == 1:
    #                         space[x] = "LMU Fault detected,Z1"
    #                         threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #                     else:
    #                         pass
    #                 except Exception as e :
    #                     self.log.error(f"{e,traceback.format_exc()}")
    #         elif self.dynamicconfig['batterytype'] in (2284,2399):
    #             for x in range(len(self.data)):
    #                 try:
    #                     if (self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0) and (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3) and (self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == False) and (self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == False) and flag_temp == 0 and flag == 0:
    #                         flag_temp = 1
    #                         temp[x] = "LMU Fault detected,Z1"
    #                         temp_threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #                         start = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
    #                     elif (self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0) and (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3) and (self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == False) and (self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == False) and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 600 and flag_temp == 1 and flag == 0:
    #                         temp[x] = "LMU Fault detected,Z1"
    #                         temp_threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #                     elif (self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0) and (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] > 3) and (self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == False) and (self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == False) and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 600 and flag_temp == 1 and flag == 0:
    #                         flag_temp = 0 
    #                         flag = 1
    #                         space.update(temp) 
    #                         space[x] = "LMU Fault detected,Z1"
    #                         threshold.update(temp_threshold)
    #                         threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
    #                         temp= {}
    #                         temp_threshold = {}
                        
    #                     elif (self.data.loc[x, self.shandler.get_value(['features', 'lmufailalarm'])] == True ) or ((self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] == 0) and (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] <= 3 )) or (self.data.loc[x, self.shandler.get_value(['features', 'battdischga'])] == 0 ) or (self.data.loc[x, self.shandler.get_value(['features', 'ebavail'])] == True) : 
    #                         flag = 0
    #                         flag_temp = 0
    #                         temp = {}
    #                         temp_threshold = {}
    #                         start = None
    #                     elif flag == 1:
    #                         space[x] = "LMU Fault detected,Z1"
    #                         threshold[x] = [{"Alarm":"LMUF","AlarmLog":[{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Battery Voltage","Value":self.data.loc[x, self.shandler.get_value(['features', 'battv'])]}]}]
                        
    #                     else:
    #                         pass
                    
    #                 except Exception as e :
    #                     self.log.error(f"{e,traceback.format_exc()}")
    #         else :
    #             pass
    #     self.glob_tally['lmu'], self.glob_spike['lmu'] = self.continuous_data(space.copy())
    #     self.glob_threshold['lmu'] ,spike = self.continuous_data(threshold.copy())
    #     del space,temp ,threshold ,temp_threshold , spike 


    def spmsoverload(self) -> None :
        limitcheck = {80:[],90:[],100:[],120:[]}
        alarm = None
        threshold = {}
        selection_dict = {}
        threshold_dict = {}
        

        try : 
            customerid = self.dynamicconfig['customerid'][0]
        except Exception as e :
            customerid = 0 
        
        try:
            capacity = float(self.dynamicconfig['capacity'][0])
        except Exception as e :
            capacity = 0 
        if capacity != 0 and customerid == 69: 

            netah = (capacity*1000)/50
            ah_dict = {80:netah*0.8,90:netah*0.9,100:netah,120:netah*1.2}
            ah_list = [80,90,100,120]
            for ah in ah_list:
                space = {}
                temp_space = {}
                for x in range(len(self.data)):
                    load = (self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])] + self.data.loc[x, self.shandler.get_value(['features', 'battchga'])])
                    if load > ah_dict[ah] :
                        space[x] = "SMPS Overloading,Z1"
                        temp_space[x] = [{"Alarm":"SMPSO","AlarmLog":[{"Name":"Loading(%)","Value":round(load/netah*100,2)},{"Name":"RMKW","Value":capacity},{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Charging Current","Value":self.data.loc[x, self.shandler.get_value(['features', 'battchga'])]}]}]

                
                limitcheck[ah],spike = self.continuous_data(space.copy())
                threshold_dict[ah] = temp_space.copy()
                del spike ,space

                selection_dict[ah] =  len(limitcheck[ah])

            sorted_by_values = dict(sorted(selection_dict.items(), key=lambda item: item[0] , reverse= True))

            
            c = 0
            for x in list(sorted_by_values.keys()) :
                if c == 1 :
                    c = 0
                    break
                if sorted_by_values[x] != 0 :
                    c = 1
                    alarm = x

            if alarm is not None:
                self.glob_tally['smpsoverload'] = limitcheck[alarm]
                for x in list(limitcheck[alarm].keys()) :
                    threshold[x] = threshold_dict[alarm][x]
                    # threshold[x] = [{"Alarm":"SMPSO","AlarmLog":[{"Name":"Loading(%)","Value":alarm},{"Name":"RMKW","Value":capacity},{"Name":"LoadCurrent","Value":self.data.loc[x, self.shandler.get_value(['features', 'loadcurrent'])]},{"Name":"Charging Current","Value":self.data.loc[x, self.shandler.get_value(['features', 'battchga'])]}]}]
                self.glob_threshold['smpsoverload'] = threshold
        del limitcheck, threshold, selection_dict,threshold_dict
        

    def batteryovercharging(self) -> None:
        temp = {}
        space = {}

        def label_row(row):
            if row[self.shandler.get_value(['features', 'battchga'])] > 0 or row[self.shandler.get_value(['features', 'battdischga'])] == 0:
                return 'C'  # Charging
            elif row[self.shandler.get_value(['features', 'battdischga'])] > 0:
                return 'D'  # Discharging
            else:
                return None  # Neither
        # if self.dynamicconfig['circleid'] ==  44:

        try : 
            customerid = self.dynamicconfig['customerid'][0]
        except Exception as e :
            customerid = 0 

        if customerid != 0:
            try:
                # Assume Battery capacity is 100Ah
                battCap = float(self.dynamicconfig['batterycapacity'])

            except Exception as e:
                battCap = False

            if self.dynamicconfig['batterytype'] in (2284,2399):
                limit = float(0.15)
            elif self.dynamicconfig['batterytype'] == 2283:
                limit = float(0.25)
            else:
                limit = False
                
            if battCap and limit:
                try:
                    site_final_gap_mdg = self.data.copy()
                    site_final_gap_mdg[self.shandler.get_value(['features', 'sentdate'])] = pd.to_datetime(site_final_gap_mdg[self.shandler.get_value(['features', 'sentdate'])])
                    filter_time = pd.to_datetime(self.dynamicconfig['runtime']) + timedelta(minutes=60)
                    # site_final_gap_mdg = site_final_gap_mdg[site_final_gap_mdg[self.shandler.get_value(['features', 'sentdate'])]>=filter_time].reset_index(drop= True)


                    site_final_gap_mdg['ChargeLabel'] = site_final_gap_mdg.apply(label_row, axis=1)

                    # Create group identifier for consecutive labels
                    site_final_gap_mdg['Group'] = (site_final_gap_mdg['ChargeLabel'] != site_final_gap_mdg['ChargeLabel'].shift()).cumsum()

                    # Calculate time differences for each group
                    group_durations = site_final_gap_mdg.groupby('Group').agg(
                        StartDate=('SentDate', 'first'),
                        EndDate=('SentDate', 'last'),
                        Label=('ChargeLabel', 'first')
                    )
                    group_durations['Duration'] = group_durations['EndDate'] - group_durations['StartDate']

                    # Add duration in hours
                    group_durations['DurationHours'] = group_durations['Duration'].dt.total_seconds() / 3600

                    # Create the dictionary
                    result_dict = {
                        idx: (row['Label'], round(row['DurationHours'], 2))
                        for idx, row in group_durations.iterrows()
                    }

                    temp_list = list(site_final_gap_mdg[site_final_gap_mdg['ChargeLabel'] == 'C']['Group'].unique())
                    opened = {}
                    closed = {}
                    alarm = {}
                    if len(temp_list) != 0:
                        for y in temp_list:
                            space_o={}
                            space_c={}
                            for x in np.where((site_final_gap_mdg['Group']==y) & (site_final_gap_mdg['SentDate'] > filter_time))[0]:
                                if site_final_gap_mdg.loc[x,'BattChgA'] > battCap*limit :
                                    space_o[x] = "Open BHCR,B10"
                                    
                                elif site_final_gap_mdg.loc[x,'BattChgA'] < battCap*limit:
                                    if y > 1 and result_dict[y-1][-1] >= 1  :
                                        space_c[x] = "Close BHCR,B11"
                                    else:
                                        pass
                                    
                            
                            space_o,spike = self.continuous_data(space_o,2)
                            space_c,spike = self.continuous_data(space_c,2)
                            
                            if len(space_o) > 0:
                                opened.update(space_o)
                                
                                alarm[y] = (True , [{"Alarm":"BHCR","AlarmLog":[{"Name":"BBAH","Value":battCap},{"Name":"Charge Current","Value":site_final_gap_mdg.loc[list(space_o.keys())[0],'BattChgA']}]}])
                                
                            elif len(space_c) > 0:
                                closed.update(space_c)
                                alarm[y] = (False, [{"Alarm":"BHCR","AlarmLog":[{"Name":"BBAH","Value":battCap},{"Name":"Charge Current","Value":site_final_gap_mdg.loc[list(space_c.keys())[0],'BattChgA']}]}])
                                    
                                    
                        space.update(opened)
                        space.update(closed)

                        group = []
                        if len(space) != 0:
                            
                            for value in space:
                                group.append(site_final_gap_mdg.loc[value,'Group'])
                            group=list(set(group))

                            for grp in group:
                                temp[grp] = alarm[grp]
                    del site_final_gap_mdg
                except Exception as e:
                    self.log.error("Traceback: %s", traceback.format_exc())
                    pass

        self.glob_tally['battovercharge'] = temp.copy()
        self.glob_tally['battovercharge_i'] = space.copy()
        del space,temp
            




    # def batteryovercharging(self) -> None:
    #     temp = {}
    #     space = {}
    #     try:
    #         # Assume Battery capacity is 100Ah
    #         battCap = float(self.dynamicconfig['batterycapacity'])

    #         site_final_gap_mdg = self.data.copy()
    #         site_final_gap_mdg[self.shandler.get_value(['features', 'sentdate'])] = pd.to_datetime(site_final_gap_mdg[self.shandler.get_value(['features', 'sentdate'])])
    #         filter_time = pd.to_datetime(self.dynamicconfig['runtime']) + timedelta(minutes=60)
    #         site_final_gap_mdg = site_final_gap_mdg[site_final_gap_mdg[self.shandler.get_value(['features', 'sentdate'])]>=filter_time].reset_index(drop= True)

            

            
    #         """Check battery charging rate is greater then 10% in vrla and 25% for lib"""
    #         if self.dynamicconfig['batterytype'] in (2284,2399):
    #             limit = float(10)
    #         elif self.dynamicconfig['batterytype'] == 2283:
    #             limit = float(25)
    #         else:
    #             pass

    #         try:
    #             site_final_gap_mdg['group'] = 0
    #             group_counter = 0
                
                  
    #             for i in range(len(site_final_gap_mdg)):
    #                 if self.dynamicconfig['batterytype'] in (2284,2399):
    #                     condition = (site_final_gap_mdg.loc[i, self.shandler.get_value(['features', 'battchga'])] != 0 and site_final_gap_mdg.loc[i, self.shandler.get_value(['features', 'battv'])] < 53.5 )
    #                 elif self.dynamicconfig['batterytype'] == 2283:
                      
    #                     condition = (site_final_gap_mdg.loc[i, self.shandler.get_value(['features', 'battchga'])] != 0 and ((site_final_gap_mdg.loc[i, self.shandler.get_value(['features', 'battcomfailalarm'])] == True and site_final_gap_mdg.loc[i, self.shandler.get_value(['features', 'battv'])] <= 53.5) or (site_final_gap_mdg.loc[i, self.shandler.get_value(['features', 'battcomfailalarm'])] == False and site_final_gap_mdg.loc[i, self.shandler.get_value(['features', 'soc'])] <= 99)))
                    
    #                 if condition:  # Check if the value is non-zero
    #                     if i == 0 or site_final_gap_mdg.loc[i - 1, self.shandler.get_value(['features', 'battchga'])] == 0:  # Increment group only after a zero
    #                         group_counter += 1
    #                     site_final_gap_mdg.loc[i, 'group'] = group_counter

    #             try:
    #                 site_final_gap_mdg['Ah'] = 0
    #                 for i in range(len(site_final_gap_mdg)-1):
    #                     if site_final_gap_mdg.loc[i,'group'] != 0:
    #                         site_final_gap_mdg.loc[i,'Ah'] = site_final_gap_mdg.loc[i,self.shandler.get_value(['features', 'battchga'])]*(site_final_gap_mdg.loc[i+1,self.shandler.get_value(['features', 'sentdate'])] - site_final_gap_mdg.loc[i,self.shandler.get_value(['features', 'sentdate'])]).total_seconds()/3600

    #                 # Iterate over unique groups
    #                 for x in site_final_gap_mdg['group'].unique():
    #                     if x != 0:  # Skip zero group (assumed not charging)
    #                         group_data = site_final_gap_mdg.loc[site_final_gap_mdg['group'] == x]
                            
    #                         # Calculate total charge (Ah) and time span (hours)
    #                         total_charge = group_data['Ah'].sum()
    #                         time_span_hours = round((group_data[self.shandler.get_value(['features', 'sentdate'])].max() - group_data[self.shandler.get_value(['features', 'sentdate'])].min()).total_seconds() / 3600, 2)
                            
    #                         # Avoid division by zero for time_span_hours
    #                         if time_span_hours > 0:
    #                             charging_rate = round(total_charge / time_span_hours / battCap, 2)*100
    #                             # Store result
    #                             for y in tuple(np.where(site_final_gap_mdg['group'] == x)[0]):
    #                                 temp[y] = "Battery High Charging Rate,B10"
    #                             space[x] = charging_rate >= limit
    #                         del group_data
    #             except Exception as e:
    #                 self.log.error("Traceback: %s", traceback.format_exc())
    #                 pass

    #         except Exception as e:
    #             self.log.error("Traceback: %s", traceback.format_exc())
    #             pass
            
    #         del site_final_gap_mdg
    #     except Exception as e :
    #         self.log.error("Traceback: %s", traceback.format_exc())
    #         pass
    #     self.glob_tally['battovercharge'] = space.copy()
    #     self.glob_tally['battovercharge_i'] = temp.copy()
    #     del space,temp

    def lowphasevoltage1(self) -> None:
        """
        Check EBAvail condition based on phase voltage.
        Low Voltage Alarm <140V if persists for one Hrs
        Updates the 'space' dictionary with indices indicating faulty EBAvail conditions.

        Returns:
        None
        """
        try : 
            customerid = self.dynamicconfig['customerid'][0]
        except Exception as e :
            customerid = 0 

        if customerid == 69:
            limit = float(190)
        else:
            limit = float(140)
        base = float(0)
        start = None
        space = {}
        temp = {}
        temp_threshold = {}
        threshold = {}
        flag = 0

        def timediff(x):
            test = x - start
            return test.total_seconds()

        for x in range(len(self.data)):

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])] > 0:
                rphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])])/2
            else:
                rphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])]

            if self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])] > 0:
                bphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])])/2
            else:
                bphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])]
            
            if self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])] > 0 and self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])] > 0:
                yphase = (self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])])/2
            else:
                yphase = self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])]

            


            try:
                if ((base < rphase < limit) or ( base < yphase < limit ) or ( base < bphase <limit)) and flag == 0:
                    flag = 1
                    start = self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]
                    temp[x] = f"Low Voltage Alarm (less than {limit} for more than 1hr),A6"
                    temp_threshold[x] = [{"Alarm":"EB_LV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"1hr"}]}]
                elif ((base < rphase <limit) or ( base <  yphase <limit) or ( base < bphase <limit)) and flag == 1 and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) < 3600:
                    temp[x] = f"Low Voltage Alarm (less than {limit} for more than 1hr),A6"
                    temp_threshold[x] = [{"Alarm":"EB_LV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"1hr"}]}]
                elif ((base < rphase <limit) or ( base <  yphase <limit) or ( base < bphase <limit)) and flag == 1 and timediff(self.data.loc[x, self.shandler.get_value(['features', 'sentdate'])]) >= 3600:
                    space.update(temp) 
                    space[x] = f"Low Voltage Alarm (less than {limit} for more than 1hr),A6"
                    threshold.update(temp_threshold)
                    threshold[x] = [{"Alarm":"EB_LV","AlarmLog":[{"Name":"RPhaseVoltage","Value":rphase},{"Name":"BPhaseVoltage","Value":bphase},{"Name":"YPhaseVoltage","Value":yphase},{"Name":"Duration","Value":"1hr"}]}]
                    temp= {}
                    temp_threshold = {}
                elif (((rphase >= limit or rphase == base) and ( yphase >= limit or yphase == base) and ( bphase >= limit or bphase == base)) or ((self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphasebvolt'])] + self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaseyvolt'])] + self.data.loc[x, self.shandler.get_value(['features', 'ebphaservoltrect'])]+self.data.loc[x, self.shandler.get_value(['features', 'ebphaservolt'])]) == base)) and flag == 1 :
                    flag = 0
                    temp = {}
                    temp_threshold = {}
                else:
                    pass
            except Exception as e :
                # print(e)
                pass

        self.glob_tally['lowPhaseVoltage1'] = space.copy()
        self.glob_threshold['lowPhaseVoltage1'] = threshold.copy()
        del space,temp

    def continuous_data(self, space: dict ,const: int = 1) -> tuple:
        """
        Identify continuous data points and categorize them into spike or anomaly.

        Parameters:
        - space (dict): A dictionary containing data points.

        Returns:
        - tuple: A tuple containing two dictionaries - one for anomalies and one for spikes.
        """
        spike_dict = {}
        anomal_dict = {}
        input_list = list(space.keys()) 
        result_list = []
        sublist = []
        # Identify continuous data points and group them
        for i in range(len(input_list)):
            if i == 0 or input_list[i] != input_list[i-1] + 1:
                if len(sublist) > 0:
                    result_list.append(sublist)
                sublist = [input_list[i]]
            else:
                sublist.append(input_list[i])
        # Append the last sublist if it exists
        if len(sublist) > 0:
            result_list.append(sublist)
        # Categorize into spikes and anomalies
        for sub_list in result_list:
            if len(sub_list) == 1:
                spike_dict[sub_list[0]] = space[sub_list[0]]
            else:
                sub_list.sort()
                tdiff = self.data.loc[sub_list[-1], self.shandler.get_value(['features', 'sentdate'])] - self.data.loc[sub_list[0], self.shandler.get_value(['features', 'sentdate'])]
                timediff = tdiff.total_seconds()
                if timediff <= 300 * const: # Threshold for spikes
                    for ind in sub_list:
                        spike_dict[ind] = space[ind]
                else:
                    for ind in sub_list:
                        anomal_dict[ind] = space[ind]
        del result_list, sublist
        return anomal_dict,spike_dict
        
            
    def loop(self) -> pd.DataFrame:
        # print(self.dynamicconfig)
        self.glob_tally = self.tally.copy()
        self.glob_spike = self.tally.copy()
        self.glob_threshold = self.threshhold.copy()
        self.data.sort_values(by=[self.shandler.get_value(['features', 'sentdate'])], inplace=True)
        self.data.reset_index(inplace=True, drop=True)
        if self.rtuname != 'CRMU':
            self.highphasevoltage2()
            self.highphasevoltage1()
            self.lowphasevoltage2()
            self.lowphasevoltage1()
            self.batteryovercharging()
            self.dcemsensor()
            self.main_condition()
        else :
            self.highphasevoltage2()
            self.highphasevoltage1()
            self.lowphasevoltage2()
            self.lowphasevoltage1()
            self.batteryovercharging()
            self.spmsoverload()
            self.rmfail()
            # self.lmufaulty()
            self.ctrfaulty() 
            self.dcemsensor()
            self.main_condition()
    
        return self.data , self.alarmdata
