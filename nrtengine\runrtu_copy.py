import warnings
from datetime import datetime ,timedelta
from src.util.accessconfig import Staticconfig 
from src.db.query import (MSSQLQuery , MYSQLQuery)
from src.models.model import Detect 
from src.common.util import (Utility, DelLog)
from src.preprocess.datapreprocess import Preprocess
from concurrent.futures import ThreadPoolExecutor
from copy import deepcopy
from tqdm import tqdm
from src.common.s3aws import S3FileHandler
import time
import logging


warnings.filterwarnings('ignore')  

if __name__ == "__main__":

    
    
    config= {"batterytype": None,
        "capacitycheck": None,
        "day": None,
        "metaindex": None,
        "rtuname": None,
        "rundate": None,
        "sigma": None,
        "siteconfig": None,
        "siteid": None,
        "soccheck": None,
        "siteclassification": None,
        "customerid":None,
        "pm":None,
        "dg": None,
        "alarms": None,
        "lastdaydata":None}
        
    shandler = Staticconfig()
    
    mssqlquery = MSSQLQuery()
    mysqlquery = MYSQLQuery()
        
    config['metaindex'] = mysqlquery.metaindex()
    current_time = datetime.now()
    # current_time = datetime.strptime("2024-10-21 14:00:00", r"%Y-%m-%d %H:%M:%S")
    todaydate = current_time.date()
    config['rundate']=todaydate
    config['day'] = 1
    config['currenttime'] = current_time.strftime(r"%Y/%m/%d %H:%M:%S")
    runtime = current_time - timedelta(hours=3)
    config['runtime'] = runtime.strftime(r"%Y/%m/%d %H:%M:%S")
    datebase = mysqlquery.alarmdata()
    Dynamicconfig = config.copy()

    

    def process_site_data(data):
        mssql_query_obj = MSSQLQuery()
        mysql_query_obj = MYSQLQuery()
        
        siteid = data['siteid']
        Dynamicconfig = deepcopy(data['Dynamicconfig'])
        rtu = Dynamicconfig['rtu']
        Dynamicconfig['siteid'] = str(siteid)
        Dynamicconfig['batterytype'] = mssql_query_obj.BatteryCheck(siteid = Dynamicconfig['siteid'])
        Dynamicconfig['siteclassification'] = mssql_query_obj.SiteClassification(siteid = Dynamicconfig['siteid'])
        Dynamicconfig['customerid'] = mssql_query_obj.customerid(siteid = Dynamicconfig['siteid'])
        Dynamicconfig['siteconfig'] = mssql_query_obj.configdata(Dynamicconfig = Dynamicconfig)
        Dynamicconfig['alarms'] = mssql_query_obj.alarmopen(Dynamicconfig = Dynamicconfig)
        if rtu == 2158:
            Dynamicconfig["dg"]=mssql_query_obj.dgcheck(siteid =Dynamicconfig['siteid'])
        rawdata = data['groupeddata'].copy()
        preprocessobj = Preprocess(Dynamicconfig=Dynamicconfig,rawdata=rawdata)
        unlabeldata = preprocessobj.processdata().copy()
        database = mysql_query_obj.alarmdata()
        detectobj = Detect(Dynamicconfig=Dynamicconfig,data=unlabeldata,database = database, mssqlquery = mssql_query_obj, log = log )
        delivery , alarmdata = detectobj.loop()[:]
        mysql_query_obj.sqldump(dumpdata = delivery,Dynamicconfig=Dynamicconfig)
        # if alarmdata.shape[0] > 0:
        #     mysql_query_obj.alarmdump(dumpdata = alarmdata,Dynamicconfig=Dynamicconfig)
        # mysql_query_obj.deletenoalarm()
        mssql_query_obj.conn.close()
        mysql_query_obj.conn.close()
        del mssql_query_obj,mysql_query_obj,unlabeldata,preprocessobj,rawdata,Dynamicconfig,detectobj,delivery,database

    
    for rtu in [2081,2158,2370]:   
        rtuname = shandler.get_value(['rtutype'])
        if rtu in rtuname.keys():
            Dynamicconfig['rtuname'] = rtuname[rtu]
            Dynamicconfig['rtu'] = rtu
            handler = S3FileHandler(Dynamicconfig)
            handler.log_file_create()
            log = handler.logger()
            fulldata = mssqlquery.rawdata(Dynamicconfig=Dynamicconfig,rtu = rtu)
            fulldata['Site_Id'] = fulldata['Site_Id'].astype(int)
            groupeddata = fulldata.groupby(shandler.get_value(['features', 'site_id']))
            temp_site_list = [{'siteid':s, 'Dynamicconfig': Dynamicconfig , 'groupeddata' : groupeddata.get_group(s) } for s in fulldata.Site_Id.unique()] 
            with ThreadPoolExecutor(max_workers=3) as executer:
                output = list(tqdm(executer.map(process_site_data, temp_site_list), total=len(temp_site_list)))
            handler.upload_file()
            handler.delete_files()
            del handler, groupeddata, fulldata, log , temp_site_list , output
        DelLog(f"{Dynamicconfig['rundate'].strftime('%Y-%m-%d')}-{Dynamicconfig['rtu']}.log")








